#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去烟算法测试和评估脚本
测试不同去烟算法的性能和效果
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from pathlib import Path
import json
from typing import List, Dict, Tu<PERSON>

from dehazing import DehazeProcessor, calculate_image_quality_metrics
from utils import visualize_image_with_bbox

class DehazingEvaluator:
    """去烟算法评估器"""
    
    def __init__(self, output_dir: str = "results/dehazing"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置matplotlib后端
        import matplotlib
        matplotlib.use('Agg')
        
    def create_synthetic_hazy_image(self, clear_image: np.ndarray, 
                                   beta: float = 1.0, A: float = 0.8) -> np.ndarray:
        """
        创建合成雾霾图像用于测试
        
        Args:
            clear_image: 清晰图像
            beta: 散射系数
            A: 大气光值
            
        Returns:
            合成的雾霾图像
        """
        h, w = clear_image.shape[:2]
        
        # 生成深度图（简化为距离中心的距离）
        center_x, center_y = w // 2, h // 2
        y, x = np.ogrid[:h, :w]
        depth = np.sqrt((x - center_x)**2 + (y - center_y)**2) / np.sqrt(center_x**2 + center_y**2)
        
        # 计算透射率
        transmission = np.exp(-beta * depth)
        
        # 扩展到三通道
        if len(clear_image.shape) == 3:
            transmission = np.stack([transmission] * 3, axis=2)
        
        # 归一化
        clear_norm = clear_image.astype(np.float32) / 255.0
        
        # 应用大气散射模型
        hazy = clear_norm * transmission + A * (1 - transmission)
        
        # 添加噪声
        noise = np.random.normal(0, 0.02, hazy.shape)
        hazy = np.clip(hazy + noise, 0, 1)
        
        return (hazy * 255).astype(np.uint8)
    
    def evaluate_single_image(self, image_path: str, methods: List[str] = None) -> Dict:
        """
        评估单张图像的去烟效果
        
        Args:
            image_path: 图像路径
            methods: 要测试的方法列表
            
        Returns:
            评估结果
        """
        if methods is None:
            methods = ["dcp"]  # 默认只测试DCP，因为AOD需要预训练权重
            
        # 读取原始图像
        original = cv2.imread(image_path)
        if original is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        # 调整图像尺寸以加快处理速度
        original = cv2.resize(original, (640, 480))
        
        # 创建合成雾霾图像
        hazy = self.create_synthetic_hazy_image(original, beta=1.5, A=0.7)
        
        results = {
            "image_path": image_path,
            "image_size": original.shape,
            "methods": {}
        }
        
        # 测试每种方法
        for method in methods:
            print(f"测试方法: {method}")
            
            try:
                # 创建处理器
                processor = DehazeProcessor(method=method, device="cpu")
                
                # 处理图像
                start_time = time.time()
                dehazed, process_info = processor.process(hazy)
                processing_time = time.time() - start_time
                
                # 计算质量指标
                quality_metrics = calculate_image_quality_metrics(hazy, dehazed)
                
                # 保存结果
                method_results = {
                    "processing_time_ms": processing_time * 1000,
                    "process_info": process_info,
                    "quality_metrics": quality_metrics
                }
                
                results["methods"][method] = method_results
                
                # 保存图像
                image_name = Path(image_path).stem
                cv2.imwrite(str(self.output_dir / f"{image_name}_{method}_hazy.jpg"), hazy)
                cv2.imwrite(str(self.output_dir / f"{image_name}_{method}_dehazed.jpg"), dehazed)
                
                print(f"  处理时间: {processing_time*1000:.2f}ms")
                print(f"  信息熵提升: {quality_metrics['entropy_improvement']:.3f}")
                print(f"  平均梯度提升: {quality_metrics['gradient_improvement']:.3f}")
                
            except Exception as e:
                print(f"  方法 {method} 处理失败: {e}")
                results["methods"][method] = {"error": str(e)}
                
        return results
    
    def create_comparison_visualization(self, image_path: str, methods: List[str] = None):
        """
        创建对比可视化图
        
        Args:
            image_path: 图像路径
            methods: 要比较的方法
        """
        if methods is None:
            methods = ["dcp"]
            
        # 读取原始图像
        original = cv2.imread(image_path)
        if original is None:
            return
            
        original = cv2.resize(original, (640, 480))
        
        # 创建雾霾图像
        hazy = self.create_synthetic_hazy_image(original, beta=1.5, A=0.7)
        
        # 准备子图
        num_images = 2 + len(methods)  # 原图 + 雾霾图 + 各种方法
        cols = min(4, num_images)
        rows = (num_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 4*rows))
        if rows == 1:
            axes = [axes] if cols == 1 else axes
        else:
            axes = axes.flatten()
            
        # 显示原图
        axes[0].imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        axes[0].set_title("原始图像")
        axes[0].axis('off')
        
        # 显示雾霾图
        axes[1].imshow(cv2.cvtColor(hazy, cv2.COLOR_BGR2RGB))
        axes[1].set_title("合成雾霾图像")
        axes[1].axis('off')
        
        # 处理并显示各种方法的结果
        for i, method in enumerate(methods):
            try:
                processor = DehazeProcessor(method=method, device="cpu")
                dehazed, info = processor.process(hazy)
                
                axes[i+2].imshow(cv2.cvtColor(dehazed, cv2.COLOR_BGR2RGB))
                axes[i+2].set_title(f"{method.upper()}\n({info['processing_time_ms']:.1f}ms)")
                axes[i+2].axis('off')
                
            except Exception as e:
                axes[i+2].text(0.5, 0.5, f"Error: {str(e)}", 
                              ha='center', va='center', transform=axes[i+2].transAxes)
                axes[i+2].set_title(f"{method.upper()} (失败)")
                axes[i+2].axis('off')
        
        # 隐藏多余的子图
        for i in range(num_images, len(axes)):
            axes[i].axis('off')
            
        plt.tight_layout()
        
        # 保存图像
        image_name = Path(image_path).stem
        plt.savefig(self.output_dir / f"{image_name}_comparison.png", 
                   dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"对比图已保存: {image_name}_comparison.png")
    
    def benchmark_performance(self, test_images: List[str], methods: List[str] = None) -> Dict:
        """
        性能基准测试
        
        Args:
            test_images: 测试图像列表
            methods: 测试方法列表
            
        Returns:
            基准测试结果
        """
        if methods is None:
            methods = ["dcp"]
            
        benchmark_results = {
            "test_images": len(test_images),
            "methods": {},
            "summary": {}
        }
        
        for method in methods:
            print(f"\n基准测试方法: {method}")
            
            method_results = {
                "processing_times": [],
                "quality_metrics": [],
                "success_count": 0,
                "error_count": 0
            }
            
            processor = DehazeProcessor(method=method, device="cpu")
            
            for img_path in test_images:
                try:
                    # 读取和预处理图像
                    image = cv2.imread(img_path)
                    if image is None:
                        continue
                        
                    image = cv2.resize(image, (640, 480))
                    hazy = self.create_synthetic_hazy_image(image)
                    
                    # 处理图像
                    start_time = time.time()
                    dehazed, info = processor.process(hazy)
                    processing_time = time.time() - start_time
                    
                    # 计算质量指标
                    quality = calculate_image_quality_metrics(hazy, dehazed)
                    
                    method_results["processing_times"].append(processing_time * 1000)
                    method_results["quality_metrics"].append(quality)
                    method_results["success_count"] += 1
                    
                except Exception as e:
                    print(f"  处理失败 {img_path}: {e}")
                    method_results["error_count"] += 1
                    
            # 计算统计信息
            if method_results["processing_times"]:
                times = method_results["processing_times"]
                method_results["avg_processing_time"] = np.mean(times)
                method_results["std_processing_time"] = np.std(times)
                method_results["min_processing_time"] = np.min(times)
                method_results["max_processing_time"] = np.max(times)
                
                # 质量指标统计
                if method_results["quality_metrics"]:
                    entropy_improvements = [q["entropy_improvement"] for q in method_results["quality_metrics"]]
                    gradient_improvements = [q["gradient_improvement"] for q in method_results["quality_metrics"]]
                    
                    method_results["avg_entropy_improvement"] = np.mean(entropy_improvements)
                    method_results["avg_gradient_improvement"] = np.mean(gradient_improvements)
                    
                print(f"  成功处理: {method_results['success_count']} 张图像")
                print(f"  平均处理时间: {method_results['avg_processing_time']:.2f}ms")
                print(f"  平均信息熵提升: {method_results['avg_entropy_improvement']:.3f}")
                print(f"  平均梯度提升: {method_results['avg_gradient_improvement']:.3f}")
                
            benchmark_results["methods"][method] = method_results
            
        # 保存基准测试结果
        with open(self.output_dir / "benchmark_results.json", "w", encoding="utf-8") as f:
            # 转换numpy类型
            def convert_numpy(obj):
                if isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                elif isinstance(obj, dict):
                    return {key: convert_numpy(value) for key, value in obj.items()}
                return obj
                
            json.dump(convert_numpy(benchmark_results), f, ensure_ascii=False, indent=2)
            
        return benchmark_results
    
    def run_comprehensive_evaluation(self, num_test_images: int = 10):
        """
        运行综合评估
        
        Args:
            num_test_images: 测试图像数量
        """
        print("开始去烟算法综合评估...")
        
        # 获取测试图像
        image_dirs = ["wuxi_video_2/image", "wuxi_video_2/images"]
        test_images = []
        
        for image_dir in image_dirs:
            if os.path.exists(image_dir):
                image_files = list(Path(image_dir).glob("*.jpg"))
                test_images.extend(image_files[:num_test_images])
                break
                
        if not test_images:
            print("未找到测试图像，创建合成测试图像...")
            # 创建合成测试图像
            os.makedirs("temp_test_images", exist_ok=True)
            for i in range(num_test_images):
                synthetic_img = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
                cv2.imwrite(f"temp_test_images/test_{i}.jpg", synthetic_img)
                test_images.append(f"temp_test_images/test_{i}.jpg")
        
        test_images = [str(img) for img in test_images[:num_test_images]]
        print(f"使用 {len(test_images)} 张图像进行测试")
        
        # 测试方法
        methods = ["dcp"]  # 目前只测试DCP方法
        
        # 单图像详细评估
        if test_images:
            print("\n单图像详细评估...")
            detailed_result = self.evaluate_single_image(test_images[0], methods)
            
            # 创建对比可视化
            print("\n创建对比可视化...")
            self.create_comparison_visualization(test_images[0], methods)
        
        # 性能基准测试
        print("\n性能基准测试...")
        benchmark_results = self.benchmark_performance(test_images, methods)
        
        # 生成评估报告
        self.generate_evaluation_report(benchmark_results)
        
        print(f"\n评估完成！结果保存在: {self.output_dir}")
        
    def generate_evaluation_report(self, benchmark_results: Dict):
        """生成评估报告"""
        report = []
        report.append("# 去烟算法评估报告\n")
        report.append(f"测试图像数量: {benchmark_results['test_images']}\n")
        report.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for method, results in benchmark_results["methods"].items():
            report.append(f"## {method.upper()} 方法\n")
            
            if "avg_processing_time" in results:
                report.append(f"- 成功处理: {results['success_count']} 张图像\n")
                report.append(f"- 失败数量: {results['error_count']} 张图像\n")
                report.append(f"- 平均处理时间: {results['avg_processing_time']:.2f}ms\n")
                report.append(f"- 处理时间范围: {results['min_processing_time']:.2f}ms - {results['max_processing_time']:.2f}ms\n")
                
                if "avg_entropy_improvement" in results:
                    report.append(f"- 平均信息熵提升: {results['avg_entropy_improvement']:.3f}\n")
                    report.append(f"- 平均梯度提升: {results['avg_gradient_improvement']:.3f}\n")
                    
                # 性能评估
                target_time = 100  # 目标处理时间100ms
                if results['avg_processing_time'] <= target_time:
                    report.append(f"- ✅ 满足实时性要求 (<{target_time}ms)\n")
                else:
                    report.append(f"- ❌ 未满足实时性要求 (>{target_time}ms)\n")
                    
            else:
                report.append("- 所有图像处理失败\n")
                
            report.append("\n")
            
        # 保存报告
        with open(self.output_dir / "evaluation_report.md", "w", encoding="utf-8") as f:
            f.writelines(report)
            
        print("评估报告已保存: evaluation_report.md")

if __name__ == "__main__":
    # 创建评估器并运行评估
    evaluator = DehazingEvaluator()
    evaluator.run_comprehensive_evaluation(num_test_images=5)
