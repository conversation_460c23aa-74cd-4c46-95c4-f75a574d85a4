#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人体检测模块
基于YOLO的人体目标检测算法
"""

import cv2
import numpy as np
import torch
from ultralytics import YOL<PERSON>
import time
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import json

class HumanDetector:
    """
    人体检测器
    基于YOLO模型的人体目标检测
    """
    
    def __init__(self, model_path: str = None, model_size: str = "n", 
                 confidence_threshold: float = 0.5, iou_threshold: float = 0.45,
                 device: str = "cpu"):
        """
        初始化人体检测器
        
        Args:
            model_path: 预训练模型路径
            model_size: 模型大小 (n, s, m, l, x)
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            device: 计算设备
        """
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.device = device
        
        # 加载模型
        if model_path and Path(model_path).exists():
            print(f"加载自定义模型: {model_path}")
            self.model = YOLO(model_path)
        else:
            print(f"加载预训练YOLOv8{model_size}模型")
            self.model = YOLO(f"yolov8{model_size}.pt")
            
        # 设置设备
        self.model.to(device)
        
        # 类别名称（COCO数据集中person类别的ID是0）
        self.class_names = ["person"]
        
    def detect(self, image: np.ndarray, return_crops: bool = False) -> Dict:
        """
        检测图像中的人体目标
        
        Args:
            image: 输入图像 (BGR格式)
            return_crops: 是否返回检测到的目标裁剪图像
            
        Returns:
            检测结果字典
        """
        start_time = time.time()
        
        # 运行推理
        results = self.model(image, 
                           conf=self.confidence_threshold,
                           iou=self.iou_threshold,
                           verbose=False)
        
        processing_time = time.time() - start_time
        
        # 解析结果
        detections = []
        crops = []
        
        if results and len(results) > 0:
            result = results[0]
            
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
                confidences = result.boxes.conf.cpu().numpy()
                classes = result.boxes.cls.cpu().numpy()
                
                for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                    # 只保留person类别（COCO中person的类别ID是0）
                    if int(cls) == 0:  # person class
                        x1, y1, x2, y2 = map(int, box)
                        
                        detection = {
                            "bbox": [x1, y1, x2, y2],
                            "confidence": float(conf),
                            "class_id": int(cls),
                            "class_name": "person"
                        }
                        detections.append(detection)
                        
                        # 提取目标裁剪图像
                        if return_crops:
                            crop = image[y1:y2, x1:x2]
                            crops.append(crop)
        
        result_dict = {
            "detections": detections,
            "num_detections": len(detections),
            "processing_time_ms": processing_time * 1000,
            "image_shape": image.shape
        }
        
        if return_crops:
            result_dict["crops"] = crops
            
        return result_dict
    
    def detect_batch(self, images: List[np.ndarray]) -> List[Dict]:
        """
        批量检测
        
        Args:
            images: 图像列表
            
        Returns:
            检测结果列表
        """
        start_time = time.time()
        
        # 批量推理
        results = self.model(images,
                           conf=self.confidence_threshold,
                           iou=self.iou_threshold,
                           verbose=False)
        
        total_processing_time = time.time() - start_time
        
        batch_results = []
        
        for i, (image, result) in enumerate(zip(images, results)):
            detections = []
            
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                classes = result.boxes.cls.cpu().numpy()
                
                for box, conf, cls in zip(boxes, confidences, classes):
                    if int(cls) == 0:  # person class
                        x1, y1, x2, y2 = map(int, box)
                        
                        detection = {
                            "bbox": [x1, y1, x2, y2],
                            "confidence": float(conf),
                            "class_id": int(cls),
                            "class_name": "person"
                        }
                        detections.append(detection)
            
            result_dict = {
                "detections": detections,
                "num_detections": len(detections),
                "image_index": i,
                "image_shape": image.shape
            }
            batch_results.append(result_dict)
        
        # 添加批处理信息
        for result in batch_results:
            result["batch_processing_time_ms"] = total_processing_time * 1000
            result["avg_processing_time_ms"] = total_processing_time * 1000 / len(images)
            
        return batch_results
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict],
                           save_path: str = None) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            detections: 检测结果
            save_path: 保存路径
            
        Returns:
            带有检测框的图像
        """
        vis_image = image.copy()
        
        for detection in detections:
            bbox = detection["bbox"]
            confidence = detection["confidence"]
            class_name = detection["class_name"]
            
            x1, y1, x2, y2 = bbox
            
            # 绘制边界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # 标签背景
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # 标签文字
            cv2.putText(vis_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
        
        if save_path:
            cv2.imwrite(save_path, vis_image)
            
        return vis_image
    
    def evaluate_on_dataset(self, dataset_path: str, output_dir: str = "results/detection") -> Dict:
        """
        在数据集上评估模型性能
        
        Args:
            dataset_path: 数据集路径
            output_dir: 输出目录
            
        Returns:
            评估结果
        """
        from ultralytics import YOLO
        
        # 使用YOLO内置的验证功能
        results = self.model.val(data=dataset_path, 
                               conf=self.confidence_threshold,
                               iou=self.iou_threshold,
                               save_json=True,
                               project=output_dir)
        
        # 提取关键指标
        metrics = {
            "mAP50": float(results.box.map50),
            "mAP50_95": float(results.box.map),
            "precision": float(results.box.mp),
            "recall": float(results.box.mr),
            "f1_score": 2 * (results.box.mp * results.box.mr) / (results.box.mp + results.box.mr) if (results.box.mp + results.box.mr) > 0 else 0
        }
        
        return metrics

class HumanDetectionTrainer:
    """
    人体检测模型训练器
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = config.get("device", "cpu")
        
    def train(self, dataset_config: str, model_size: str = "n", epochs: int = 100):
        """
        训练人体检测模型
        
        Args:
            dataset_config: 数据集配置文件路径
            model_size: 模型大小
            epochs: 训练轮数
        """
        print(f"开始训练YOLOv8{model_size}人体检测模型...")
        
        # 加载预训练模型
        model = YOLO(f"yolov8{model_size}.pt")
        
        # 开始训练
        results = model.train(
            data=dataset_config,
            epochs=epochs,
            imgsz=self.config.get("image_size", 640),
            batch=self.config.get("batch_size", 16),
            device=self.device,
            project="models/detection",
            name=f"yolov8{model_size}_human",
            save=True,
            save_period=10,
            val=True,
            plots=True,
            verbose=True
        )
        
        print("训练完成！")
        return results

def calculate_detection_metrics(predictions: List[Dict], ground_truths: List[Dict],
                              iou_threshold: float = 0.5) -> Dict:
    """
    计算检测指标
    
    Args:
        predictions: 预测结果列表
        ground_truths: 真实标注列表
        iou_threshold: IoU阈值
        
    Returns:
        评估指标
    """
    def calculate_iou(box1, box2):
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    total_tp = 0  # True Positives
    total_fp = 0  # False Positives
    total_fn = 0  # False Negatives
    
    for pred, gt in zip(predictions, ground_truths):
        pred_boxes = [det["bbox"] for det in pred["detections"]]
        gt_boxes = [det["bbox"] for det in gt["detections"]]
        
        # 匹配预测框和真实框
        matched_gt = set()
        
        for pred_box in pred_boxes:
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt_box in enumerate(gt_boxes):
                if gt_idx in matched_gt:
                    continue
                    
                iou = calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            if best_iou >= iou_threshold:
                total_tp += 1
                matched_gt.add(best_gt_idx)
            else:
                total_fp += 1
        
        # 未匹配的真实框为False Negatives
        total_fn += len(gt_boxes) - len(matched_gt)
    
    # 计算指标
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    metrics = {
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        "true_positives": total_tp,
        "false_positives": total_fp,
        "false_negatives": total_fn
    }
    
    return metrics

if __name__ == "__main__":
    # 测试人体检测器
    print("测试人体检测器...")
    
    # 创建检测器
    detector = HumanDetector(model_size="n", device="cpu")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 480, 3), dtype=np.uint8)
    
    # 运行检测
    results = detector.detect(test_image)
    
    print(f"检测结果:")
    print(f"  检测到 {results['num_detections']} 个目标")
    print(f"  处理时间: {results['processing_time_ms']:.2f}ms")
    
    # 可视化结果
    if results['detections']:
        vis_image = detector.visualize_detections(test_image, results['detections'])
        print("检测结果已可视化")
    
    print("人体检测器测试完成！")
