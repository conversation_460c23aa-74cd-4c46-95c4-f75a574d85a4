#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
检查项目运行所需的环境和依赖
"""

import sys
import os
import platform
import subprocess
import importlib
from pathlib import Path
import torch
import cv2
import numpy as np

class EnvironmentChecker:
    def __init__(self):
        self.results = {}
        self.issues = []
        
    def check_python_version(self):
        """检查Python版本"""
        print("=" * 50)
        print("Python环境检查")
        print("=" * 50)
        
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        
        print(f"Python版本: {version_str}")
        print(f"Python路径: {sys.executable}")
        
        # 检查版本要求
        if version.major == 3 and version.minor >= 8:
            status = "✓ 通过"
        else:
            status = "✗ 失败"
            self.issues.append("Python版本需要3.8或更高")
            
        print(f"版本检查: {status}")
        
        self.results["python"] = {
            "version": version_str,
            "path": sys.executable,
            "status": "pass" if "通过" in status else "fail"
        }
        
    def check_system_info(self):
        """检查系统信息"""
        print("\n" + "=" * 50)
        print("系统信息")
        print("=" * 50)
        
        system_info = {
            "操作系统": platform.system(),
            "系统版本": platform.release(),
            "架构": platform.machine(),
            "处理器": platform.processor(),
            "Python实现": platform.python_implementation()
        }
        
        for key, value in system_info.items():
            print(f"{key}: {value}")
            
        self.results["system"] = system_info
        
    def check_gpu_environment(self):
        """检查GPU环境"""
        print("\n" + "=" * 50)
        print("GPU环境检查")
        print("=" * 50)
        
        gpu_info = {
            "cuda_available": torch.cuda.is_available(),
            "cuda_version": None,
            "device_count": 0,
            "devices": []
        }
        
        if torch.cuda.is_available():
            gpu_info["cuda_version"] = torch.version.cuda
            gpu_info["device_count"] = torch.cuda.device_count()
            
            print(f"CUDA可用: ✓ 是")
            print(f"CUDA版本: {gpu_info['cuda_version']}")
            print(f"GPU设备数量: {gpu_info['device_count']}")
            
            for i in range(gpu_info['device_count']):
                device_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                gpu_info["devices"].append({
                    "id": i,
                    "name": device_name,
                    "memory_gb": round(memory_total, 2)
                })
                print(f"  GPU {i}: {device_name} ({memory_total:.2f}GB)")
                
        else:
            print("CUDA可用: ✗ 否")
            print("将使用CPU进行计算")
            
        self.results["gpu"] = gpu_info
        
    def check_required_packages(self):
        """检查必需的Python包"""
        print("\n" + "=" * 50)
        print("Python包检查")
        print("=" * 50)
        
        required_packages = {
            "torch": "PyTorch深度学习框架",
            "torchvision": "PyTorch视觉库",
            "cv2": "OpenCV图像处理库",
            "numpy": "数值计算库",
            "matplotlib": "绘图库",
            "pandas": "数据处理库",
            "PIL": "图像处理库",
            "yaml": "YAML配置文件解析",
            "tqdm": "进度条库",
            "ultralytics": "YOLO框架",
            "albumentations": "数据增强库"
        }
        
        package_status = {}
        
        for package, description in required_packages.items():
            try:
                module = importlib.import_module(package.replace("-", "_"))
                version = getattr(module, "__version__", "未知")
                status = "✓ 已安装"
                package_status[package] = {
                    "installed": True,
                    "version": version,
                    "description": description
                }
                print(f"{package:20} {version:15} {status}")
                
            except ImportError:
                status = "✗ 未安装"
                package_status[package] = {
                    "installed": False,
                    "version": None,
                    "description": description
                }
                print(f"{package:20} {'N/A':15} {status}")
                self.issues.append(f"缺少包: {package} ({description})")
                
        self.results["packages"] = package_status
        
    def check_opencv_functionality(self):
        """检查OpenCV功能"""
        print("\n" + "=" * 50)
        print("OpenCV功能检查")
        print("=" * 50)
        
        opencv_info = {
            "version": cv2.__version__,
            "build_info": {},
            "video_codecs": [],
            "image_formats": []
        }
        
        print(f"OpenCV版本: {opencv_info['version']}")
        
        # 检查视频编解码器支持
        try:
            # 创建测试视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            test_writer = cv2.VideoWriter('test.mp4', fourcc, 20.0, (640, 480))
            if test_writer.isOpened():
                opencv_info["video_codecs"].append("mp4v")
                test_writer.release()
                os.remove('test.mp4')
                print("视频编解码器: ✓ 支持")
            else:
                print("视频编解码器: ✗ 不支持")
                self.issues.append("OpenCV视频编解码器不可用")
                
        except Exception as e:
            print(f"视频编解码器检查失败: {e}")
            self.issues.append("OpenCV视频编解码器检查失败")
            
        # 检查图像格式支持
        try:
            test_img = np.zeros((100, 100, 3), dtype=np.uint8)
            for fmt in ['.jpg', '.png', '.bmp']:
                test_file = f'test{fmt}'
                success = cv2.imwrite(test_file, test_img)
                if success:
                    opencv_info["image_formats"].append(fmt)
                    os.remove(test_file)
                    
            print(f"图像格式支持: {', '.join(opencv_info['image_formats'])}")
            
        except Exception as e:
            print(f"图像格式检查失败: {e}")
            
        self.results["opencv"] = opencv_info
        
    def check_dataset_structure(self):
        """检查数据集结构"""
        print("\n" + "=" * 50)
        print("数据集结构检查")
        print("=" * 50)
        
        dataset_path = Path("wuxi_video_2")
        
        required_dirs = [
            "image",
            "images", 
            "labels",
            "labels/train",
            "labels/val"
        ]
        
        dataset_info = {
            "path": str(dataset_path),
            "exists": dataset_path.exists(),
            "structure": {}
        }
        
        if dataset_path.exists():
            print(f"数据集路径: ✓ 存在 ({dataset_path.absolute()})")
            
            for dir_name in required_dirs:
                dir_path = dataset_path / dir_name
                exists = dir_path.exists()
                
                if exists:
                    # 统计文件数量
                    if dir_name in ["image", "images"]:
                        file_count = len(list(dir_path.glob("*.jpg")))
                    elif "labels" in dir_name:
                        file_count = len(list(dir_path.glob("*.txt")))
                    else:
                        file_count = len(list(dir_path.iterdir()))
                        
                    status = f"✓ 存在 ({file_count}个文件)"
                else:
                    file_count = 0
                    status = "✗ 不存在"
                    if dir_name not in ["image", "images"]:  # image和images二选一即可
                        self.issues.append(f"缺少目录: {dir_name}")
                        
                dataset_info["structure"][dir_name] = {
                    "exists": exists,
                    "file_count": file_count
                }
                
                print(f"  {dir_name:20} {status}")
                
        else:
            print(f"数据集路径: ✗ 不存在 ({dataset_path.absolute()})")
            self.issues.append("数据集目录不存在")
            
        self.results["dataset"] = dataset_info
        
    def check_project_structure(self):
        """检查项目结构"""
        print("\n" + "=" * 50)
        print("项目结构检查")
        print("=" * 50)
        
        required_dirs = [
            "src",
            "configs", 
            "models",
            "results",
            "logs"
        ]
        
        project_info = {"structure": {}}
        
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            exists = dir_path.exists()
            
            if exists:
                status = "✓ 存在"
            else:
                status = "✗ 不存在"
                # 自动创建目录
                dir_path.mkdir(exist_ok=True)
                status += " (已创建)"
                
            project_info["structure"][dir_name] = exists
            print(f"  {dir_name:20} {status}")
            
        self.results["project"] = project_info
        
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "=" * 50)
        print("环境检查报告")
        print("=" * 50)
        
        if len(self.issues) == 0:
            print("✓ 所有检查项目都通过了！环境配置正确。")
        else:
            print(f"✗ 发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
                
        # 保存详细报告
        import json
        with open("environment_check_report.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
            
        print(f"\n详细报告已保存到: environment_check_report.json")
        
        return len(self.issues) == 0
        
    def run_all_checks(self):
        """运行所有检查"""
        print("开始环境检查...")
        
        self.check_python_version()
        self.check_system_info()
        self.check_gpu_environment()
        self.check_required_packages()
        self.check_opencv_functionality()
        self.check_dataset_structure()
        self.check_project_structure()
        
        return self.generate_report()

if __name__ == "__main__":
    checker = EnvironmentChecker()
    success = checker.run_all_checks()
    
    if success:
        print("\n🎉 环境检查完成，可以开始项目开发！")
        sys.exit(0)
    else:
        print("\n⚠️  请解决上述问题后再继续。")
        sys.exit(1)
