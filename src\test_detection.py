#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人体检测测试脚本
测试检测算法的性能和效果
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from pathlib import Path
import json
from typing import List, Dict, Tuple
import random

class MockDetector:
    """
    模拟检测器
    用于在没有预训练模型时测试检测流程
    """
    
    def __init__(self, confidence_threshold: float = 0.5):
        self.confidence_threshold = confidence_threshold
        
    def detect(self, image: np.ndarray) -> Dict:
        """
        模拟检测过程
        
        Args:
            image: 输入图像
            
        Returns:
            模拟检测结果
        """
        start_time = time.time()
        
        # 模拟处理时间
        time.sleep(0.01)  # 10ms
        
        h, w = image.shape[:2]
        
        # 生成随机检测结果
        num_detections = random.randint(0, 3)
        detections = []
        
        for i in range(num_detections):
            # 随机生成边界框
            x1 = random.randint(0, w//2)
            y1 = random.randint(0, h//2)
            x2 = random.randint(x1 + 50, min(x1 + 200, w))
            y2 = random.randint(y1 + 100, min(y1 + 300, h))
            
            confidence = random.uniform(self.confidence_threshold, 1.0)
            
            detection = {
                "bbox": [x1, y1, x2, y2],
                "confidence": confidence,
                "class_id": 0,
                "class_name": "person"
            }
            detections.append(detection)
            
        processing_time = time.time() - start_time
        
        return {
            "detections": detections,
            "num_detections": len(detections),
            "processing_time_ms": processing_time * 1000,
            "image_shape": image.shape
        }

class DetectionEvaluator:
    """检测算法评估器"""
    
    def __init__(self, output_dir: str = "results/detection"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置matplotlib后端
        import matplotlib
        matplotlib.use('Agg')
        
    def load_ground_truth_annotations(self, annotation_path: str) -> List[Dict]:
        """
        加载真实标注
        
        Args:
            annotation_path: 标注文件路径
            
        Returns:
            标注列表
        """
        annotations = []
        
        if os.path.exists(annotation_path):
            with open(annotation_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            
                            annotations.append({
                                "class_id": class_id,
                                "x_center": x_center,
                                "y_center": y_center,
                                "width": width,
                                "height": height
                            })
                            
        return annotations
        
    def yolo_to_xyxy(self, yolo_bbox: Dict, img_width: int, img_height: int) -> List[int]:
        """将YOLO格式转换为xyxy格式"""
        x_center = yolo_bbox["x_center"] * img_width
        y_center = yolo_bbox["y_center"] * img_height
        width = yolo_bbox["width"] * img_width
        height = yolo_bbox["height"] * img_height
        
        x1 = int(x_center - width / 2)
        y1 = int(y_center - height / 2)
        x2 = int(x_center + width / 2)
        y2 = int(y_center + height / 2)
        
        return [x1, y1, x2, y2]
        
    def visualize_detections(self, image: np.ndarray, detections: List[Dict],
                           ground_truth: List[Dict] = None, save_path: str = None) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            detections: 检测结果
            ground_truth: 真实标注
            save_path: 保存路径
            
        Returns:
            可视化图像
        """
        vis_image = image.copy()
        h, w = image.shape[:2]
        
        # 绘制真实标注（蓝色）
        if ground_truth:
            for gt in ground_truth:
                bbox = self.yolo_to_xyxy(gt, w, h)
                x1, y1, x2, y2 = bbox
                
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
                cv2.putText(vis_image, "GT", (x1, y1-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # 绘制检测结果（绿色）
        for detection in detections:
            bbox = detection["bbox"]
            confidence = detection["confidence"]
            
            x1, y1, x2, y2 = bbox
            
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            label = f"Person: {confidence:.2f}"
            cv2.putText(vis_image, label, (x1, y1-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        if save_path:
            cv2.imwrite(save_path, vis_image)
            
        return vis_image
        
    def calculate_iou(self, box1: List[int], box2: List[int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
        
    def evaluate_single_image(self, image_path: str, annotation_path: str = None) -> Dict:
        """
        评估单张图像
        
        Args:
            image_path: 图像路径
            annotation_path: 标注路径
            
        Returns:
            评估结果
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        # 调整图像尺寸
        image = cv2.resize(image, (640, 480))
        h, w = image.shape[:2]
        
        # 创建检测器
        detector = MockDetector()
        
        # 运行检测
        detection_results = detector.detect(image)
        
        # 加载真实标注
        ground_truth = []
        if annotation_path and os.path.exists(annotation_path):
            ground_truth = self.load_ground_truth_annotations(annotation_path)
            
        # 计算评估指标
        metrics = self.calculate_metrics(detection_results["detections"], ground_truth, w, h)
        
        # 可视化结果
        image_name = Path(image_path).stem
        vis_path = self.output_dir / f"{image_name}_detection.jpg"
        self.visualize_detections(image, detection_results["detections"], 
                                ground_truth, str(vis_path))
        
        result = {
            "image_path": image_path,
            "detection_results": detection_results,
            "ground_truth_count": len(ground_truth),
            "metrics": metrics,
            "visualization_path": str(vis_path)
        }
        
        return result
        
    def calculate_metrics(self, detections: List[Dict], ground_truth: List[Dict],
                         img_width: int, img_height: int, iou_threshold: float = 0.5) -> Dict:
        """计算评估指标"""
        if not ground_truth:
            return {
                "precision": 0.0,
                "recall": 0.0,
                "f1_score": 0.0,
                "true_positives": 0,
                "false_positives": len(detections),
                "false_negatives": 0
            }
            
        # 转换真实标注格式
        gt_boxes = []
        for gt in ground_truth:
            bbox = self.yolo_to_xyxy(gt, img_width, img_height)
            gt_boxes.append(bbox)
            
        # 匹配检测结果和真实标注
        matched_gt = set()
        true_positives = 0
        
        for detection in detections:
            pred_box = detection["bbox"]
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt_box in enumerate(gt_boxes):
                if gt_idx in matched_gt:
                    continue
                    
                iou = self.calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
                    
            if best_iou >= iou_threshold:
                true_positives += 1
                matched_gt.add(best_gt_idx)
                
        false_positives = len(detections) - true_positives
        false_negatives = len(gt_boxes) - len(matched_gt)
        
        # 计算指标
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1_score": f1_score,
            "true_positives": true_positives,
            "false_positives": false_positives,
            "false_negatives": false_negatives
        }
        
    def benchmark_performance(self, test_images: List[str], num_tests: int = 50) -> Dict:
        """性能基准测试"""
        print(f"开始性能基准测试，测试图像数量: {len(test_images)}")
        
        detector = MockDetector()
        processing_times = []
        
        for i in range(num_tests):
            # 随机选择测试图像
            image_path = random.choice(test_images)
            image = cv2.imread(image_path)
            
            if image is None:
                continue
                
            image = cv2.resize(image, (640, 480))
            
            # 测试检测速度
            start_time = time.time()
            results = detector.detect(image)
            processing_time = time.time() - start_time
            
            processing_times.append(processing_time * 1000)  # 转换为毫秒
            
        if processing_times:
            avg_time = np.mean(processing_times)
            std_time = np.std(processing_times)
            min_time = np.min(processing_times)
            max_time = np.max(processing_times)
            
            benchmark_results = {
                "num_tests": len(processing_times),
                "avg_processing_time_ms": avg_time,
                "std_processing_time_ms": std_time,
                "min_processing_time_ms": min_time,
                "max_processing_time_ms": max_time,
                "fps": 1000 / avg_time,
                "meets_realtime_requirement": avg_time <= 200  # 200ms目标
            }
            
            print(f"性能测试结果:")
            print(f"  平均处理时间: {avg_time:.2f}ms")
            print(f"  标准差: {std_time:.2f}ms")
            print(f"  时间范围: {min_time:.2f}ms - {max_time:.2f}ms")
            print(f"  平均FPS: {1000/avg_time:.1f}")
            
            if avg_time <= 200:
                print(f"  ✅ 满足实时性要求 (<200ms)")
            else:
                print(f"  ❌ 未满足实时性要求 (>200ms)")
                
            return benchmark_results
        else:
            return {"error": "无有效测试结果"}
            
    def run_comprehensive_evaluation(self, num_test_images: int = 10):
        """运行综合评估"""
        print("开始人体检测算法综合评估...")
        
        # 获取测试图像
        image_dirs = ["wuxi_video_2/image", "wuxi_video_2/images"]
        test_images = []
        
        for image_dir in image_dirs:
            if os.path.exists(image_dir):
                image_files = list(Path(image_dir).glob("*.jpg"))
                test_images.extend(image_files[:num_test_images])
                break
                
        if not test_images:
            print("未找到测试图像，创建合成测试图像...")
            os.makedirs("temp_test_images", exist_ok=True)
            for i in range(num_test_images):
                synthetic_img = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
                cv2.imwrite(f"temp_test_images/test_{i}.jpg", synthetic_img)
                test_images.append(f"temp_test_images/test_{i}.jpg")
                
        test_images = [str(img) for img in test_images[:num_test_images]]
        print(f"使用 {len(test_images)} 张图像进行测试")
        
        # 单图像详细评估
        if test_images:
            print("\n单图像详细评估...")
            
            # 查找对应的标注文件
            image_path = test_images[0]
            image_name = Path(image_path).stem
            
            # 尝试找到对应的标注文件
            annotation_paths = [
                f"wuxi_video_2/labels/train/{image_name}.txt",
                f"wuxi_video_2/labels/val/{image_name}.txt"
            ]
            
            annotation_path = None
            for ann_path in annotation_paths:
                if os.path.exists(ann_path):
                    annotation_path = ann_path
                    break
                    
            detailed_result = self.evaluate_single_image(image_path, annotation_path)
            print(f"检测到 {detailed_result['detection_results']['num_detections']} 个目标")
            print(f"处理时间: {detailed_result['detection_results']['processing_time_ms']:.2f}ms")
            
            if annotation_path:
                metrics = detailed_result['metrics']
                print(f"评估指标:")
                print(f"  精确率: {metrics['precision']:.3f}")
                print(f"  召回率: {metrics['recall']:.3f}")
                print(f"  F1分数: {metrics['f1_score']:.3f}")
        
        # 性能基准测试
        print("\n性能基准测试...")
        benchmark_results = self.benchmark_performance(test_images)
        
        # 生成评估报告
        self.generate_evaluation_report(benchmark_results)
        
        print(f"\n评估完成！结果保存在: {self.output_dir}")
        
    def generate_evaluation_report(self, benchmark_results: Dict):
        """生成评估报告"""
        report = []
        report.append("# 人体检测算法评估报告\n")
        report.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if "error" not in benchmark_results:
            report.append("## 性能测试结果\n")
            report.append(f"- 测试次数: {benchmark_results['num_tests']}\n")
            report.append(f"- 平均处理时间: {benchmark_results['avg_processing_time_ms']:.2f}ms\n")
            report.append(f"- 处理时间标准差: {benchmark_results['std_processing_time_ms']:.2f}ms\n")
            report.append(f"- 处理时间范围: {benchmark_results['min_processing_time_ms']:.2f}ms - {benchmark_results['max_processing_time_ms']:.2f}ms\n")
            report.append(f"- 平均FPS: {benchmark_results['fps']:.1f}\n")
            
            if benchmark_results['meets_realtime_requirement']:
                report.append("- ✅ 满足实时性要求 (<200ms)\n")
            else:
                report.append("- ❌ 未满足实时性要求 (>200ms)\n")
        else:
            report.append("## 性能测试失败\n")
            report.append(f"错误: {benchmark_results['error']}\n")
            
        report.append("\n## 说明\n")
        report.append("本次测试使用模拟检测器进行性能评估。\n")
        report.append("实际部署时需要使用训练好的YOLO模型。\n")
        
        # 保存报告
        with open(self.output_dir / "evaluation_report.md", "w", encoding="utf-8") as f:
            f.writelines(report)
            
        print("评估报告已保存: evaluation_report.md")

if __name__ == "__main__":
    # 创建评估器并运行评估
    evaluator = DetectionEvaluator()
    evaluator.run_comprehensive_evaluation(num_test_images=5)
