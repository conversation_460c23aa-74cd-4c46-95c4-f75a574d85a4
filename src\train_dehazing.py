#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去烟算法训练脚本
用于训练AOD-Net等深度学习去烟模型
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import cv2
import numpy as np
from pathlib import Path
import yaml
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Tuple, List
import time

from dehazing import AODNet, calculate_image_quality_metrics
from utils import set_seed, load_config

class SyntheticHazeDataset(Dataset):
    """
    合成雾霾数据集
    由于缺少配对的清晰-有雾图像，我们使用合成方法生成训练数据
    """
    
    def __init__(self, image_dir: str, transform=None, haze_params: dict = None):
        """
        初始化数据集
        
        Args:
            image_dir: 清晰图像目录
            transform: 数据变换
            haze_params: 雾霾合成参数
        """
        self.image_dir = Path(image_dir)
        self.transform = transform
        
        # 默认雾霾参数
        self.haze_params = haze_params or {
            "beta_range": (0.5, 2.0),      # 散射系数范围
            "A_range": (0.7, 1.0),         # 大气光范围
            "depth_range": (0.1, 1.0)      # 深度范围
        }
        
        # 获取所有图像文件
        self.image_files = list(self.image_dir.glob("*.jpg"))
        if len(self.image_files) == 0:
            # 如果没有jpg，尝试其他格式
            for ext in ["*.png", "*.jpeg", "*.bmp"]:
                self.image_files.extend(list(self.image_dir.glob(ext)))
                
        print(f"找到 {len(self.image_files)} 张图像用于训练")
        
    def synthesize_haze(self, clear_image: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        合成雾霾图像
        使用大气散射模型: I = J*t + A*(1-t)
        其中 I是有雾图像，J是清晰图像，t是透射率，A是大气光
        """
        h, w, c = clear_image.shape
        
        # 随机生成参数
        beta = np.random.uniform(*self.haze_params["beta_range"])
        A = np.random.uniform(*self.haze_params["A_range"])
        
        # 生成随机深度图（简化为随机噪声）
        depth = np.random.uniform(*self.haze_params["depth_range"], (h, w))
        
        # 计算透射率 t = exp(-beta * depth)
        transmission = np.exp(-beta * depth)
        
        # 扩展到三通道
        transmission = np.stack([transmission] * c, axis=2)
        
        # 归一化图像到[0,1]
        clear_norm = clear_image.astype(np.float32) / 255.0
        
        # 合成有雾图像
        hazy = clear_norm * transmission + A * (1 - transmission)
        
        # 添加噪声
        noise = np.random.normal(0, 0.01, hazy.shape)
        hazy = np.clip(hazy + noise, 0, 1)
        
        # 转换回[0,255]
        hazy_image = (hazy * 255).astype(np.uint8)
        
        params = {
            "beta": beta,
            "A": A,
            "transmission_mean": float(np.mean(transmission))
        }
        
        return hazy_image, params
        
    def __len__(self):
        return len(self.image_files)
        
    def __getitem__(self, idx):
        # 读取清晰图像
        image_path = self.image_files[idx]
        clear_image = cv2.imread(str(image_path))
        
        if clear_image is None:
            # 如果读取失败，返回随机图像
            clear_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
        # 调整图像尺寸
        clear_image = cv2.resize(clear_image, (640, 480))
        
        # 合成有雾图像
        hazy_image, _ = self.synthesize_haze(clear_image)
        
        # 转换为tensor
        clear_tensor = torch.from_numpy(clear_image.astype(np.float32) / 255.0).permute(2, 0, 1)
        hazy_tensor = torch.from_numpy(hazy_image.astype(np.float32) / 255.0).permute(2, 0, 1)
        
        if self.transform:
            clear_tensor = self.transform(clear_tensor)
            hazy_tensor = self.transform(hazy_tensor)
            
        return hazy_tensor, clear_tensor

class DehazingLoss(nn.Module):
    """
    去雾损失函数
    结合L1损失、感知损失和SSIM损失
    """
    
    def __init__(self, l1_weight=1.0, ssim_weight=0.1):
        super(DehazingLoss, self).__init__()
        self.l1_weight = l1_weight
        self.ssim_weight = ssim_weight
        self.l1_loss = nn.L1Loss()
        
    def ssim_loss(self, pred, target):
        """计算SSIM损失"""
        # 简化的SSIM计算
        mu_pred = torch.mean(pred)
        mu_target = torch.mean(target)
        
        sigma_pred = torch.var(pred)
        sigma_target = torch.var(target)
        sigma_pred_target = torch.mean((pred - mu_pred) * (target - mu_target))
        
        c1 = 0.01 ** 2
        c2 = 0.03 ** 2
        
        ssim = ((2 * mu_pred * mu_target + c1) * (2 * sigma_pred_target + c2)) / \
               ((mu_pred ** 2 + mu_target ** 2 + c1) * (sigma_pred + sigma_target + c2))
               
        return 1 - ssim
        
    def forward(self, pred, target):
        l1 = self.l1_loss(pred, target)
        ssim = self.ssim_loss(pred, target)
        
        total_loss = self.l1_weight * l1 + self.ssim_weight * ssim
        
        return total_loss, {"l1": l1.item(), "ssim": ssim.item()}

class DehazingTrainer:
    """去烟模型训练器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.device = torch.device(config.get("device", "cpu"))
        
        # 设置随机种子
        set_seed(config.get("seed", 42))
        
        # 初始化模型
        self.model = AODNet().to(self.device)
        
        # 初始化损失函数
        self.criterion = DehazingLoss(
            l1_weight=config.get("l1_weight", 1.0),
            ssim_weight=config.get("ssim_weight", 0.1)
        )
        
        # 初始化优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.get("learning_rate", 0.001),
            weight_decay=config.get("weight_decay", 1e-4)
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.get("epochs", 100),
            eta_min=config.get("min_lr", 1e-6)
        )
        
        # 训练历史
        self.train_history = {
            "loss": [],
            "l1_loss": [],
            "ssim_loss": [],
            "lr": []
        }
        
    def create_data_loaders(self):
        """创建数据加载器"""
        # 使用项目中的图像数据
        image_dirs = ["wuxi_video_2/image", "wuxi_video_2/images"]
        
        for image_dir in image_dirs:
            if os.path.exists(image_dir):
                dataset = SyntheticHazeDataset(image_dir)
                break
        else:
            # 如果没有找到图像目录，创建虚拟数据集
            print("警告: 未找到图像目录，使用虚拟数据集")
            dataset = SyntheticHazeDataset(".")  # 当前目录
            
        # 分割训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.get("batch_size", 4),
            shuffle=True,
            num_workers=self.config.get("num_workers", 2),
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.get("batch_size", 4),
            shuffle=False,
            num_workers=self.config.get("num_workers", 2),
            pin_memory=True
        )
        
        return train_loader, val_loader
        
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_l1 = 0
        total_ssim = 0
        
        pbar = tqdm(train_loader, desc="Training")
        
        for batch_idx, (hazy, clear) in enumerate(pbar):
            hazy = hazy.to(self.device)
            clear = clear.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(hazy)
            
            # 计算损失
            loss, loss_dict = self.criterion(output, clear)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            total_l1 += loss_dict["l1"]
            total_ssim += loss_dict["ssim"]
            
            # 更新进度条
            pbar.set_postfix({
                "Loss": f"{loss.item():.4f}",
                "L1": f"{loss_dict['l1']:.4f}",
                "SSIM": f"{loss_dict['ssim']:.4f}"
            })
            
        avg_loss = total_loss / len(train_loader)
        avg_l1 = total_l1 / len(train_loader)
        avg_ssim = total_ssim / len(train_loader)
        
        return avg_loss, avg_l1, avg_ssim
        
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_l1 = 0
        total_ssim = 0
        
        with torch.no_grad():
            for hazy, clear in val_loader:
                hazy = hazy.to(self.device)
                clear = clear.to(self.device)
                
                output = self.model(hazy)
                loss, loss_dict = self.criterion(output, clear)
                
                total_loss += loss.item()
                total_l1 += loss_dict["l1"]
                total_ssim += loss_dict["ssim"]
                
        avg_loss = total_loss / len(val_loader)
        avg_l1 = total_l1 / len(val_loader)
        avg_ssim = total_ssim / len(val_loader)
        
        return avg_loss, avg_l1, avg_ssim
        
    def train(self):
        """开始训练"""
        print("开始训练去烟模型...")
        
        # 创建数据加载器
        train_loader, val_loader = self.create_data_loaders()
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        # 创建保存目录
        os.makedirs("models/dehazing", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        best_val_loss = float('inf')
        epochs = self.config.get("epochs", 50)
        
        for epoch in range(epochs):
            print(f"\nEpoch {epoch+1}/{epochs}")
            
            # 训练
            train_loss, train_l1, train_ssim = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_l1, val_ssim = self.validate(val_loader)
            
            # 更新学习率
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.train_history["loss"].append(train_loss)
            self.train_history["l1_loss"].append(train_l1)
            self.train_history["ssim_loss"].append(train_ssim)
            self.train_history["lr"].append(current_lr)
            
            # 打印结果
            print(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            print(f"Train L1: {train_l1:.4f}, Val L1: {val_l1:.4f}")
            print(f"Learning Rate: {current_lr:.6f}")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), "models/dehazing/best_model.pth")
                print(f"保存最佳模型 (Val Loss: {val_loss:.4f})")
                
            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scheduler_state_dict': self.scheduler.state_dict(),
                    'train_history': self.train_history,
                    'config': self.config
                }
                torch.save(checkpoint, f"models/dehazing/checkpoint_epoch_{epoch+1}.pth")
                
        # 保存训练历史
        with open("logs/dehazing_training_history.json", "w") as f:
            json.dump(self.train_history, f, indent=2)
            
        print("训练完成！")
        
    def plot_training_history(self):
        """绘制训练历史"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 损失曲线
        axes[0, 0].plot(self.train_history["loss"])
        axes[0, 0].set_title("Training Loss")
        axes[0, 0].set_xlabel("Epoch")
        axes[0, 0].set_ylabel("Loss")
        
        # L1损失
        axes[0, 1].plot(self.train_history["l1_loss"])
        axes[0, 1].set_title("L1 Loss")
        axes[0, 1].set_xlabel("Epoch")
        axes[0, 1].set_ylabel("L1 Loss")
        
        # SSIM损失
        axes[1, 0].plot(self.train_history["ssim_loss"])
        axes[1, 0].set_title("SSIM Loss")
        axes[1, 0].set_xlabel("Epoch")
        axes[1, 0].set_ylabel("SSIM Loss")
        
        # 学习率
        axes[1, 1].plot(self.train_history["lr"])
        axes[1, 1].set_title("Learning Rate")
        axes[1, 1].set_xlabel("Epoch")
        axes[1, 1].set_ylabel("Learning Rate")
        
        plt.tight_layout()
        plt.savefig("results/dehazing_training_history.png", dpi=150)
        plt.show()

if __name__ == "__main__":
    # 训练配置
    config = {
        "device": "cpu",  # 或 "cuda" 如果有GPU
        "batch_size": 4,
        "epochs": 50,
        "learning_rate": 0.001,
        "weight_decay": 1e-4,
        "min_lr": 1e-6,
        "l1_weight": 1.0,
        "ssim_weight": 0.1,
        "num_workers": 2,
        "seed": 42
    }
    
    # 创建训练器并开始训练
    trainer = DehazingTrainer(config)
    trainer.train()
    trainer.plot_training_history()
