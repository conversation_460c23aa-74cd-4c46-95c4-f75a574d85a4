# YOLO数据集配置文件
# 浓烟环境人体目标判别项目

# 数据集路径配置
path: wuxi_video_2  # 数据集根目录
train: labels/train  # 训练集标签路径（相对于path）
val: labels/val      # 验证集标签路径（相对于path）

# 类别配置
nc: 1  # 类别数量
names: ['person']  # 类别名称列表

# 数据集统计信息
dataset_info:
  total_images: 3187
  train_images: 2245
  val_images: 942
  total_objects: 3190
  image_size: [1920, 1080]
  
# 训练配置
train_config:
  batch_size: 16
  epochs: 100
  imgsz: 640
  device: 'cpu'  # 或 'cuda' 如果有GPU
  
# 数据增强配置
augmentation:
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
