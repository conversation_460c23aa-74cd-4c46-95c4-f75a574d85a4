#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双模态融合模块
结合红外和热成像数据，提升人体检测准确率
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional
import time
import json

from dehazing import DehazeProcessor
from human_detection import HumanDetector

class ModalAlignment:
    """
    模态对齐模块
    处理红外和热成像的时间同步和空间配准
    """
    
    def __init__(self, target_fps: float = 25.0):
        """
        初始化模态对齐
        
        Args:
            target_fps: 目标帧率
        """
        self.target_fps = target_fps
        self.frame_interval = 1.0 / target_fps
        
    def temporal_alignment(self, ir_frames: List[np.ndarray], 
                          thermal_frames: List[np.ndarray],
                          ir_timestamps: List[float],
                          thermal_timestamps: List[float]) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        时间对齐
        
        Args:
            ir_frames: 红外图像帧列表
            thermal_frames: 热成像帧列表
            ir_timestamps: 红外图像时间戳
            thermal_timestamps: 热成像时间戳
            
        Returns:
            对齐后的帧对
        """
        aligned_ir = []
        aligned_thermal = []
        
        # 简化的时间对齐：找到最接近的时间戳
        for i, ir_ts in enumerate(ir_timestamps):
            # 找到最接近的热成像帧
            best_idx = 0
            min_diff = float('inf')
            
            for j, thermal_ts in enumerate(thermal_timestamps):
                diff = abs(ir_ts - thermal_ts)
                if diff < min_diff:
                    min_diff = diff
                    best_idx = j
                    
            # 如果时间差小于阈值，则认为是匹配的
            if min_diff < self.frame_interval / 2:
                aligned_ir.append(ir_frames[i])
                aligned_thermal.append(thermal_frames[best_idx])
                
        return aligned_ir, aligned_thermal
        
    def spatial_registration(self, ir_frame: np.ndarray, 
                           thermal_frame: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        空间配准
        
        Args:
            ir_frame: 红外图像
            thermal_frame: 热成像
            
        Returns:
            配准后的图像对
        """
        # 确保两个图像尺寸一致
        target_size = (640, 480)
        
        ir_aligned = cv2.resize(ir_frame, target_size)
        thermal_aligned = cv2.resize(thermal_frame, target_size)
        
        # 简化的配准：假设两个相机已经校准对齐
        # 实际应用中可能需要更复杂的配准算法
        
        return ir_aligned, thermal_aligned

class SimpleFusion(nn.Module):
    """
    简化的融合网络
    """

    def __init__(self, feature_dim: int = 64):
        super(SimpleFusion, self).__init__()

        self.feature_dim = feature_dim

        # 轻量级特征提取网络
        self.ir_feature_extractor = self._make_feature_extractor()
        self.thermal_feature_extractor = self._make_feature_extractor()

        # 简化的注意力机制
        self.attention_conv = nn.Conv2d(feature_dim * 2, feature_dim, 1)

        # 融合网络
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(feature_dim * 2, feature_dim, 3, padding=1),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_dim, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )

        # 输出层
        self.output_conv = nn.Conv2d(32, 3, 1)

    def _make_feature_extractor(self):
        """创建轻量级特征提取器"""
        return nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, self.feature_dim, 3, padding=1),
            nn.BatchNorm2d(self.feature_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, ir_image: torch.Tensor, thermal_image: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            ir_image: 红外图像 (B, C, H, W)
            thermal_image: 热成像 (B, C, H, W)

        Returns:
            融合后的图像
        """
        # 提取特征
        ir_features = self.ir_feature_extractor(ir_image)
        thermal_features = self.thermal_feature_extractor(thermal_image)

        # 简化的注意力机制
        combined_features = torch.cat([ir_features, thermal_features], dim=1)
        attention_weights = torch.sigmoid(self.attention_conv(combined_features))

        # 应用注意力权重
        attended_ir = ir_features * attention_weights
        attended_thermal = thermal_features * (1 - attention_weights)

        # 特征融合
        fused_features = torch.cat([attended_ir, attended_thermal], dim=1)
        fused_features = self.fusion_conv(fused_features)

        # 生成输出图像
        output = self.output_conv(fused_features)
        output = torch.sigmoid(output)  # 确保输出在[0,1]范围内

        return output

class MultimodalFusionSystem:
    """
    多模态融合系统
    整合去烟、检测和融合功能
    """
    
    def __init__(self, config: Dict):
        """
        初始化融合系统
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = config.get("device", "cpu")
        
        # 初始化各个模块
        self.dehazer = DehazeProcessor(method="dcp", device=self.device)
        self.detector = None  # 暂时不初始化，避免下载模型
        self.modal_alignment = ModalAlignment()
        
        # 初始化融合网络
        self.fusion_network = SimpleFusion()
        self.fusion_network.to(self.device)
        self.fusion_network.eval()
        
    def preprocess_frame_pair(self, ir_frame: np.ndarray, 
                             thermal_frame: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预处理帧对
        
        Args:
            ir_frame: 红外图像
            thermal_frame: 热成像
            
        Returns:
            预处理后的帧对
        """
        # 空间对齐
        ir_aligned, thermal_aligned = self.modal_alignment.spatial_registration(
            ir_frame, thermal_frame)
        
        # 去烟处理（主要针对红外图像）
        ir_dehazed, _ = self.dehazer.process(ir_aligned)
        
        return ir_dehazed, thermal_aligned
        
    def feature_level_fusion(self, ir_frame: np.ndarray, 
                           thermal_frame: np.ndarray) -> np.ndarray:
        """
        特征级融合
        
        Args:
            ir_frame: 红外图像
            thermal_frame: 热成像
            
        Returns:
            融合后的图像
        """
        # 转换为tensor
        ir_tensor = torch.from_numpy(ir_frame.astype(np.float32) / 255.0).permute(2, 0, 1).unsqueeze(0)
        thermal_tensor = torch.from_numpy(thermal_frame.astype(np.float32) / 255.0).permute(2, 0, 1).unsqueeze(0)
        
        ir_tensor = ir_tensor.to(self.device)
        thermal_tensor = thermal_tensor.to(self.device)
        
        # 融合
        with torch.no_grad():
            fused_tensor = self.fusion_network(ir_tensor, thermal_tensor)
            
        # 转换回numpy
        fused_image = fused_tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        fused_image = (fused_image * 255).astype(np.uint8)
        
        return fused_image
        
    def decision_level_fusion(self, ir_detections: List[Dict], 
                            thermal_detections: List[Dict],
                            fusion_strategy: str = "weighted_average") -> List[Dict]:
        """
        决策级融合
        
        Args:
            ir_detections: 红外图像检测结果
            thermal_detections: 热成像检测结果
            fusion_strategy: 融合策略
            
        Returns:
            融合后的检测结果
        """
        if fusion_strategy == "weighted_average":
            return self._weighted_average_fusion(ir_detections, thermal_detections)
        elif fusion_strategy == "max_confidence":
            return self._max_confidence_fusion(ir_detections, thermal_detections)
        else:
            return self._simple_union_fusion(ir_detections, thermal_detections)
            
    def _weighted_average_fusion(self, ir_detections: List[Dict], 
                               thermal_detections: List[Dict],
                               ir_weight: float = 0.6) -> List[Dict]:
        """加权平均融合"""
        fused_detections = []
        
        # 简化的融合：对于每个红外检测，找到最接近的热成像检测
        for ir_det in ir_detections:
            ir_bbox = ir_det["bbox"]
            ir_conf = ir_det["confidence"]
            
            best_thermal = None
            best_iou = 0
            
            for thermal_det in thermal_detections:
                iou = self._calculate_iou(ir_bbox, thermal_det["bbox"])
                if iou > best_iou:
                    best_iou = iou
                    best_thermal = thermal_det
                    
            if best_thermal and best_iou > 0.3:  # IoU阈值
                # 融合边界框和置信度
                fused_bbox = [
                    int(ir_bbox[0] * ir_weight + best_thermal["bbox"][0] * (1 - ir_weight)),
                    int(ir_bbox[1] * ir_weight + best_thermal["bbox"][1] * (1 - ir_weight)),
                    int(ir_bbox[2] * ir_weight + best_thermal["bbox"][2] * (1 - ir_weight)),
                    int(ir_bbox[3] * ir_weight + best_thermal["bbox"][3] * (1 - ir_weight))
                ]
                
                fused_conf = ir_conf * ir_weight + best_thermal["confidence"] * (1 - ir_weight)
                
                fused_detections.append({
                    "bbox": fused_bbox,
                    "confidence": fused_conf,
                    "class_id": 0,
                    "class_name": "person",
                    "fusion_type": "weighted_average"
                })
            else:
                # 没有匹配的热成像检测，保留红外检测
                ir_det_copy = ir_det.copy()
                ir_det_copy["fusion_type"] = "ir_only"
                fused_detections.append(ir_det_copy)
                
        # 添加未匹配的热成像检测
        for thermal_det in thermal_detections:
            thermal_bbox = thermal_det["bbox"]
            matched = False
            
            for ir_det in ir_detections:
                iou = self._calculate_iou(thermal_bbox, ir_det["bbox"])
                if iou > 0.3:
                    matched = True
                    break
                    
            if not matched:
                thermal_det_copy = thermal_det.copy()
                thermal_det_copy["fusion_type"] = "thermal_only"
                fused_detections.append(thermal_det_copy)
                
        return fused_detections
        
    def _calculate_iou(self, box1: List[int], box2: List[int]) -> float:
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
        
    def _max_confidence_fusion(self, ir_detections: List[Dict], 
                             thermal_detections: List[Dict]) -> List[Dict]:
        """最大置信度融合"""
        all_detections = ir_detections + thermal_detections
        
        # 按置信度排序
        all_detections.sort(key=lambda x: x["confidence"], reverse=True)
        
        # 非极大值抑制
        fused_detections = []
        for detection in all_detections:
            is_duplicate = False
            
            for existing in fused_detections:
                iou = self._calculate_iou(detection["bbox"], existing["bbox"])
                if iou > 0.5:  # NMS阈值
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                detection["fusion_type"] = "max_confidence"
                fused_detections.append(detection)
                
        return fused_detections
        
    def _simple_union_fusion(self, ir_detections: List[Dict], 
                           thermal_detections: List[Dict]) -> List[Dict]:
        """简单联合融合"""
        all_detections = ir_detections + thermal_detections
        
        for detection in all_detections:
            detection["fusion_type"] = "union"
            
        return all_detections
        
    def process_frame_pair(self, ir_frame: np.ndarray, thermal_frame: np.ndarray) -> Dict:
        """
        处理帧对
        
        Args:
            ir_frame: 红外图像
            thermal_frame: 热成像
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        # 预处理
        ir_processed, thermal_processed = self.preprocess_frame_pair(ir_frame, thermal_frame)
        
        # 特征级融合
        fused_image = self.feature_level_fusion(ir_processed, thermal_processed)
        
        # 模拟检测结果（实际应用中使用真实检测器）
        ir_detections = self._mock_detection(ir_processed)
        thermal_detections = self._mock_detection(thermal_processed)
        fused_detections = self._mock_detection(fused_image)
        
        # 决策级融合
        final_detections = self.decision_level_fusion(ir_detections, thermal_detections)
        
        processing_time = time.time() - start_time
        
        result = {
            "ir_processed": ir_processed,
            "thermal_processed": thermal_processed,
            "fused_image": fused_image,
            "ir_detections": ir_detections,
            "thermal_detections": thermal_detections,
            "fused_detections": fused_detections,
            "final_detections": final_detections,
            "processing_time_ms": processing_time * 1000,
            "num_final_detections": len(final_detections)
        }
        
        return result
        
    def _mock_detection(self, image: np.ndarray) -> List[Dict]:
        """模拟检测结果"""
        import random
        
        h, w = image.shape[:2]
        num_detections = random.randint(0, 2)
        detections = []
        
        for i in range(num_detections):
            x1 = random.randint(0, w//2)
            y1 = random.randint(0, h//2)
            x2 = random.randint(x1 + 50, min(x1 + 150, w))
            y2 = random.randint(y1 + 100, min(y1 + 250, h))
            
            confidence = random.uniform(0.5, 0.95)
            
            detections.append({
                "bbox": [x1, y1, x2, y2],
                "confidence": confidence,
                "class_id": 0,
                "class_name": "person"
            })
            
        return detections

if __name__ == "__main__":
    # 测试多模态融合系统
    print("测试多模态融合系统...")
    
    # 创建配置
    config = {
        "device": "cpu"
    }
    
    # 创建融合系统
    fusion_system = MultimodalFusionSystem(config)
    
    # 创建测试图像
    ir_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    thermal_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 处理帧对
    result = fusion_system.process_frame_pair(ir_frame, thermal_frame)
    
    print(f"处理结果:")
    print(f"  处理时间: {result['processing_time_ms']:.2f}ms")
    print(f"  红外检测数量: {len(result['ir_detections'])}")
    print(f"  热成像检测数量: {len(result['thermal_detections'])}")
    print(f"  最终检测数量: {result['num_final_detections']}")
    
    print("多模态融合系统测试完成！")
