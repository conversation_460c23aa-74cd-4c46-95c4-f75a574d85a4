#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成系统
整合去烟、检测和融合功能的完整系统
"""

import cv2
import numpy as np
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dehazing import DehazeProcessor
from multimodal_fusion import MultimodalFusionSystem
from utils import load_config

class IntegratedSmokeDetectionSystem:
    """
    浓烟环境人体目标判别集成系统
    """
    
    def __init__(self, config_path: str = "configs/project_config.yaml"):
        """
        初始化集成系统
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        device_config = self.config.get("hardware", {}).get("development", {}).get("device", "cpu")

        # 处理设备配置
        if device_config == "auto":
            import torch
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device_config
        
        # 性能指标目标
        self.performance_targets = {
            "dehazing_time_ms": self.config.get("models", {}).get("dehazing", {}).get("target_time_ms", 100),
            "detection_time_ms": self.config.get("models", {}).get("detection", {}).get("target_time_ms", 200),
            "detection_accuracy": self.config.get("models", {}).get("detection", {}).get("target_accuracy", 0.8),
            "fusion_accuracy": self.config.get("models", {}).get("fusion", {}).get("target_accuracy", 0.85),
            "fusion_recall": self.config.get("models", {}).get("fusion", {}).get("target_recall", 0.999)
        }
        
        # 初始化各个模块
        self._initialize_modules()
        
        # 性能统计
        self.performance_stats = {
            "total_frames_processed": 0,
            "total_processing_time": 0,
            "dehazing_times": [],
            "detection_times": [],
            "fusion_times": [],
            "detection_counts": []
        }
        
    def _initialize_modules(self):
        """初始化各个功能模块"""
        print("初始化系统模块...")
        
        # 初始化去烟处理器
        dehazing_method = self.config.get("models", {}).get("dehazing", {}).get("type", "dcp")
        # 映射配置中的方法名到实际支持的方法
        method_mapping = {
            "aod-net": "dcp",  # 暂时使用DCP替代AOD-Net
            "aodnet": "dcp",
            "dcp": "dcp"
        }
        actual_method = method_mapping.get(dehazing_method.lower(), "dcp")
        self.dehazer = DehazeProcessor(method=actual_method, device=self.device)
        print(f"✓ 去烟模块已初始化 (方法: {dehazing_method})")
        
        # 初始化多模态融合系统
        fusion_config = {
            "device": self.device,
            "fusion_type": self.config.get("models", {}).get("fusion", {}).get("type", "attention")
        }
        self.fusion_system = MultimodalFusionSystem(fusion_config)
        print("✓ 多模态融合系统已初始化")
        
        print("系统初始化完成！")
        
    def process_single_frame(self, ir_frame: np.ndarray, 
                           thermal_frame: Optional[np.ndarray] = None) -> Dict:
        """
        处理单帧图像
        
        Args:
            ir_frame: 红外图像
            thermal_frame: 热成像（可选）
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        result = {
            "input_shape": ir_frame.shape,
            "has_thermal": thermal_frame is not None,
            "processing_stages": {}
        }
        
        # 阶段1：去烟处理
        dehazing_start = time.time()
        dehazed_frame, dehazing_info = self.dehazer.process(ir_frame)
        dehazing_time = time.time() - dehazing_start
        
        result["processing_stages"]["dehazing"] = {
            "time_ms": dehazing_time * 1000,
            "info": dehazing_info,
            "meets_target": dehazing_time * 1000 <= self.performance_targets["dehazing_time_ms"]
        }
        
        # 阶段2：多模态处理
        if thermal_frame is not None:
            # 双模态融合
            fusion_start = time.time()
            fusion_result = self.fusion_system.process_frame_pair(dehazed_frame, thermal_frame)
            fusion_time = time.time() - fusion_start
            
            result["processing_stages"]["fusion"] = {
                "time_ms": fusion_time * 1000,
                "detections": fusion_result["final_detections"],
                "num_detections": len(fusion_result["final_detections"])
            }
            
            final_detections = fusion_result["final_detections"]
            processed_frame = fusion_result["fused_image"]
            
        else:
            # 单模态处理（仅红外）
            detection_start = time.time()
            # 这里应该使用真实的检测器，暂时使用模拟结果
            final_detections = self._mock_detection(dehazed_frame)
            detection_time = time.time() - detection_start
            
            result["processing_stages"]["detection"] = {
                "time_ms": detection_time * 1000,
                "meets_target": detection_time * 1000 <= self.performance_targets["detection_time_ms"]
            }
            
            processed_frame = dehazed_frame
            
        # 总处理时间
        total_time = time.time() - start_time
        
        result.update({
            "dehazed_frame": dehazed_frame,
            "processed_frame": processed_frame,
            "detections": final_detections,
            "num_detections": len(final_detections),
            "total_processing_time_ms": total_time * 1000,
            "fps": 1000 / (total_time * 1000) if total_time > 0 else 0
        })
        
        # 更新性能统计
        self._update_performance_stats(result)
        
        return result
        
    def _mock_detection(self, image: np.ndarray) -> List[Dict]:
        """模拟检测结果"""
        import random
        
        h, w = image.shape[:2]
        num_detections = random.randint(0, 2)
        detections = []
        
        for i in range(num_detections):
            x1 = random.randint(0, w//2)
            y1 = random.randint(0, h//2)
            x2 = random.randint(x1 + 50, min(x1 + 150, w))
            y2 = random.randint(y1 + 100, min(y1 + 250, h))
            
            confidence = random.uniform(0.6, 0.95)
            
            detections.append({
                "bbox": [x1, y1, x2, y2],
                "confidence": confidence,
                "class_id": 0,
                "class_name": "person"
            })
            
        return detections
        
    def _update_performance_stats(self, result: Dict):
        """更新性能统计"""
        self.performance_stats["total_frames_processed"] += 1
        self.performance_stats["total_processing_time"] += result["total_processing_time_ms"]
        
        if "dehazing" in result["processing_stages"]:
            self.performance_stats["dehazing_times"].append(
                result["processing_stages"]["dehazing"]["time_ms"]
            )
            
        if "detection" in result["processing_stages"]:
            self.performance_stats["detection_times"].append(
                result["processing_stages"]["detection"]["time_ms"]
            )
            
        if "fusion" in result["processing_stages"]:
            self.performance_stats["fusion_times"].append(
                result["processing_stages"]["fusion"]["time_ms"]
            )
            
        self.performance_stats["detection_counts"].append(result["num_detections"])
        
    def visualize_result(self, image: np.ndarray, result: Dict, 
                        save_path: str = None) -> np.ndarray:
        """
        可视化处理结果
        
        Args:
            image: 原始图像
            result: 处理结果
            save_path: 保存路径
            
        Returns:
            可视化图像
        """
        vis_image = image.copy()
        
        # 绘制检测框
        for detection in result["detections"]:
            bbox = detection["bbox"]
            confidence = detection["confidence"]
            
            x1, y1, x2, y2 = bbox
            
            # 绘制边界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"Person: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # 标签文字
            cv2.putText(vis_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 添加性能信息
        info_text = [
            f"Detections: {result['num_detections']}",
            f"Processing: {result['total_processing_time_ms']:.1f}ms",
            f"FPS: {result['fps']:.1f}"
        ]
        
        for i, text in enumerate(info_text):
            cv2.putText(vis_image, text, (10, 30 + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        if save_path:
            cv2.imwrite(save_path, vis_image)
            
        return vis_image
        
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if self.performance_stats["total_frames_processed"] == 0:
            return {"error": "没有处理任何帧"}
            
        stats = self.performance_stats
        
        summary = {
            "total_frames": stats["total_frames_processed"],
            "avg_processing_time_ms": stats["total_processing_time"] / stats["total_frames_processed"],
            "avg_fps": 1000 / (stats["total_processing_time"] / stats["total_frames_processed"]),
            "avg_detections_per_frame": np.mean(stats["detection_counts"]) if stats["detection_counts"] else 0
        }
        
        if stats["dehazing_times"]:
            summary["dehazing"] = {
                "avg_time_ms": np.mean(stats["dehazing_times"]),
                "max_time_ms": np.max(stats["dehazing_times"]),
                "meets_target": np.mean(stats["dehazing_times"]) <= self.performance_targets["dehazing_time_ms"]
            }
            
        if stats["detection_times"]:
            summary["detection"] = {
                "avg_time_ms": np.mean(stats["detection_times"]),
                "max_time_ms": np.max(stats["detection_times"]),
                "meets_target": np.mean(stats["detection_times"]) <= self.performance_targets["detection_time_ms"]
            }
            
        if stats["fusion_times"]:
            summary["fusion"] = {
                "avg_time_ms": np.mean(stats["fusion_times"]),
                "max_time_ms": np.max(stats["fusion_times"])
            }
            
        return summary
        
    def run_system_test(self, test_images: List[str], output_dir: str = "results/system_test"):
        """
        运行系统测试
        
        Args:
            test_images: 测试图像列表
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"开始系统测试，测试图像数量: {len(test_images)}")
        
        test_results = []
        
        for i, image_path in enumerate(test_images):
            print(f"处理图像 {i+1}/{len(test_images)}: {Path(image_path).name}")
            
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"  跳过：无法读取图像")
                continue
                
            # 调整图像尺寸
            image = cv2.resize(image, (640, 480))
            
            # 处理图像
            result = self.process_single_frame(image)
            
            # 可视化结果
            vis_image = self.visualize_result(image, result)
            
            # 保存结果
            image_name = Path(image_path).stem
            output_file = output_path / f"{image_name}_result.jpg"
            cv2.imwrite(str(output_file), vis_image)
            
            # 记录结果
            test_result = {
                "image_path": image_path,
                "processing_time_ms": result["total_processing_time_ms"],
                "num_detections": result["num_detections"],
                "fps": result["fps"],
                "output_file": str(output_file)
            }
            test_results.append(test_result)
            
            print(f"  处理时间: {result['total_processing_time_ms']:.1f}ms")
            print(f"  检测数量: {result['num_detections']}")
            print(f"  FPS: {result['fps']:.1f}")
            
        # 生成测试报告
        self._generate_test_report(test_results, output_path)
        
        print(f"\n系统测试完成！结果保存在: {output_path}")
        
    def _generate_test_report(self, test_results: List[Dict], output_path: Path):
        """生成测试报告"""
        # 保存详细结果
        with open(output_path / "test_results.json", "w", encoding="utf-8") as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
            
        # 生成性能摘要
        performance_summary = self.get_performance_summary()

        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        performance_summary = convert_numpy_types(performance_summary)

        with open(output_path / "performance_summary.json", "w", encoding="utf-8") as f:
            json.dump(performance_summary, f, ensure_ascii=False, indent=2)
            
        # 生成Markdown报告
        report_lines = [
            "# 浓烟环境人体目标判别系统测试报告\n",
            f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n",
            f"测试图像数量: {len(test_results)}\n\n",
            "## 性能摘要\n"
        ]
        
        if "error" not in performance_summary:
            report_lines.extend([
                f"- 平均处理时间: {performance_summary['avg_processing_time_ms']:.2f}ms\n",
                f"- 平均FPS: {performance_summary['avg_fps']:.1f}\n",
                f"- 平均检测数量: {performance_summary['avg_detections_per_frame']:.1f}\n\n"
            ])
            
            if "dehazing" in performance_summary:
                dehazing = performance_summary["dehazing"]
                status = "✅" if dehazing["meets_target"] else "❌"
                report_lines.extend([
                    "### 去烟模块\n",
                    f"- 平均处理时间: {dehazing['avg_time_ms']:.2f}ms\n",
                    f"- 最大处理时间: {dehazing['max_time_ms']:.2f}ms\n",
                    f"- 满足目标要求: {status}\n\n"
                ])
                
            if "detection" in performance_summary:
                detection = performance_summary["detection"]
                status = "✅" if detection["meets_target"] else "❌"
                report_lines.extend([
                    "### 检测模块\n",
                    f"- 平均处理时间: {detection['avg_time_ms']:.2f}ms\n",
                    f"- 最大处理时间: {detection['max_time_ms']:.2f}ms\n",
                    f"- 满足目标要求: {status}\n\n"
                ])
                
        # 保存报告
        with open(output_path / "test_report.md", "w", encoding="utf-8") as f:
            f.writelines(report_lines)
            
        print("测试报告已生成:")
        print(f"  详细结果: test_results.json")
        print(f"  性能摘要: performance_summary.json")
        print(f"  测试报告: test_report.md")

if __name__ == "__main__":
    # 创建集成系统
    system = IntegratedSmokeDetectionSystem()
    
    # 获取测试图像
    test_images = []
    image_dirs = ["wuxi_video_2/image", "wuxi_video_2/images"]
    
    for image_dir in image_dirs:
        if Path(image_dir).exists():
            image_files = list(Path(image_dir).glob("*.jpg"))
            test_images.extend([str(f) for f in image_files[:5]])  # 取前5张图像
            break
            
    if not test_images:
        print("未找到测试图像，创建合成测试图像...")
        Path("temp_test_images").mkdir(exist_ok=True)
        for i in range(3):
            synthetic_img = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
            cv2.imwrite(f"temp_test_images/test_{i}.jpg", synthetic_img)
            test_images.append(f"temp_test_images/test_{i}.jpg")
            
    # 运行系统测试
    system.run_system_test(test_images)
