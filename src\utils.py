#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含项目中常用的工具函数
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import torch
import random
from pathlib import Path
import yaml
import json
from typing import List, Tuple, Dict, Any

def set_seed(seed=42):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def create_project_structure():
    """创建项目目录结构"""
    dirs = [
        "src",
        "data",
        "models",
        "results",
        "logs",
        "configs",
        "notebooks",
        "docs",
        "tests"
    ]
    
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("项目目录结构创建完成")

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            return yaml.safe_load(f)
        elif config_path.endswith('.json'):
            return json.load(f)
        else:
            raise ValueError("不支持的配置文件格式")

def save_config(config: Dict[str, Any], config_path: str):
    """保存配置文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        elif config_path.endswith('.json'):
            json.dump(config, f, ensure_ascii=False, indent=2)
        else:
            raise ValueError("不支持的配置文件格式")

def read_yolo_annotation(annotation_path: str) -> List[List[float]]:
    """读取YOLO格式的标注文件"""
    annotations = []
    if os.path.exists(annotation_path):
        with open(annotation_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        annotations.append([class_id, x_center, y_center, width, height])
    return annotations

def yolo_to_xyxy(bbox: List[float], img_width: int, img_height: int) -> List[int]:
    """将YOLO格式的边界框转换为xyxy格式"""
    class_id, x_center, y_center, width, height = bbox
    
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    x1 = int(x_center - width / 2)
    y1 = int(y_center - height / 2)
    x2 = int(x_center + width / 2)
    y2 = int(y_center + height / 2)
    
    return [x1, y1, x2, y2]

def xyxy_to_yolo(bbox: List[int], img_width: int, img_height: int) -> List[float]:
    """将xyxy格式的边界框转换为YOLO格式"""
    x1, y1, x2, y2 = bbox
    
    x_center = (x1 + x2) / 2 / img_width
    y_center = (y1 + y2) / 2 / img_height
    width = (x2 - x1) / img_width
    height = (y2 - y1) / img_height
    
    return [x_center, y_center, width, height]

def visualize_image_with_bbox(image_path: str, annotation_path: str = None, 
                             class_names: List[str] = None, save_path: str = None):
    """可视化图像和边界框"""
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img_height, img_width = image.shape[:2]
    
    # 读取标注
    if annotation_path and os.path.exists(annotation_path):
        annotations = read_yolo_annotation(annotation_path)
        
        # 绘制边界框
        for ann in annotations:
            class_id, x_center, y_center, width, height = ann
            x1, y1, x2, y2 = yolo_to_xyxy(ann, img_width, img_height)
            
            # 绘制边界框
            cv2.rectangle(image_rgb, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 添加类别标签
            if class_names and class_id < len(class_names):
                label = class_names[class_id]
            else:
                label = f"Class {class_id}"
            
            cv2.putText(image_rgb, label, (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # 显示图像
    plt.figure(figsize=(12, 8))
    plt.imshow(image_rgb)
    plt.title(f"Image: {os.path.basename(image_path)}")
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def calculate_image_quality_metrics(image: np.ndarray) -> Dict[str, float]:
    """计算图像质量指标"""
    # 转换为灰度图像
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image
    
    # 计算信息熵
    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
    hist = hist.flatten()
    hist = hist[hist > 0]  # 移除零值
    prob = hist / hist.sum()
    entropy = -np.sum(prob * np.log2(prob))
    
    # 计算平均梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
    avg_gradient = np.mean(gradient_magnitude)
    
    # 计算对比度（标准差）
    contrast = np.std(gray)
    
    # 计算亮度（均值）
    brightness = np.mean(gray)
    
    return {
        "entropy": entropy,
        "avg_gradient": avg_gradient,
        "contrast": contrast,
        "brightness": brightness
    }

def check_gpu_availability():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        device_name = torch.cuda.get_device_name(current_device)
        
        print(f"GPU可用: {device_count}个设备")
        print(f"当前设备: {current_device} - {device_name}")
        print(f"CUDA版本: {torch.version.cuda}")
        
        # 显示GPU内存信息
        memory_allocated = torch.cuda.memory_allocated(current_device) / 1024**3
        memory_cached = torch.cuda.memory_reserved(current_device) / 1024**3
        print(f"GPU内存使用: {memory_allocated:.2f}GB / {memory_cached:.2f}GB")
        
        return True
    else:
        print("GPU不可用，将使用CPU")
        return False

def create_yolo_config(dataset_path: str, class_names: List[str], 
                      output_path: str = "configs/dataset.yaml"):
    """创建YOLO数据集配置文件"""
    config = {
        "path": dataset_path,
        "train": "labels/train",
        "val": "labels/val",
        "nc": len(class_names),
        "names": class_names
    }
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存配置
    save_config(config, output_path)
    print(f"YOLO配置文件已保存到: {output_path}")
    
    return config

def log_experiment_info(experiment_name: str, config: Dict[str, Any], 
                       log_dir: str = "logs"):
    """记录实验信息"""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"{experiment_name}.json")
    
    experiment_info = {
        "experiment_name": experiment_name,
        "timestamp": str(pd.Timestamp.now()),
        "config": config,
        "environment": {
            "python_version": sys.version,
            "torch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
            "cuda_version": torch.version.cuda if torch.cuda.is_available() else None
        }
    }
    
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_info, f, ensure_ascii=False, indent=2)
    
    print(f"实验信息已记录到: {log_file}")

def resize_image_keep_aspect_ratio(image: np.ndarray, target_size: Tuple[int, int], 
                                  fill_color: Tuple[int, int, int] = (114, 114, 114)) -> np.ndarray:
    """保持宽高比的图像缩放"""
    h, w = image.shape[:2]
    target_w, target_h = target_size
    
    # 计算缩放比例
    scale = min(target_w / w, target_h / h)
    
    # 计算新尺寸
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # 缩放图像
    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    
    # 创建目标尺寸的画布
    canvas = np.full((target_h, target_w, 3), fill_color, dtype=np.uint8)
    
    # 计算粘贴位置（居中）
    start_x = (target_w - new_w) // 2
    start_y = (target_h - new_h) // 2
    
    # 粘贴图像
    canvas[start_y:start_y+new_h, start_x:start_x+new_w] = resized
    
    return canvas

if __name__ == "__main__":
    # 测试函数
    print("工具模块测试")
    
    # 检查GPU
    check_gpu_availability()
    
    # 创建项目结构
    create_project_structure()
    
    print("工具模块测试完成")
