#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人体检测模型训练脚本
使用YOLO框架训练人体检测模型
"""

import os
import yaml
import json
import time
from pathlib import Path
from ultralytics import YOLO
import torch

from utils import load_config, set_seed
from human_detection import HumanDetector

class DetectionTrainer:
    """人体检测模型训练器"""
    
    def __init__(self, config_path: str = "configs/project_config.yaml"):
        """
        初始化训练器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.detection_config = self.config.get("models", {}).get("detection", {})
        self.training_config = self.config.get("training", {})
        
        # 设置随机种子
        set_seed(42)
        
        # 设置设备
        self.device = self.detection_config.get("device", "cpu")
        if self.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
        print(f"使用设备: {self.device}")
        
    def prepare_dataset_config(self):
        """准备数据集配置文件"""
        dataset_config = {
            "path": "wuxi_video_2",
            "train": "labels/train",
            "val": "labels/val",
            "nc": 1,
            "names": ["person"]
        }
        
        # 保存数据集配置
        config_path = "configs/detection_dataset.yaml"
        with open(config_path, "w", encoding="utf-8") as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
            
        print(f"数据集配置已保存: {config_path}")
        return config_path
        
    def train_model(self, model_size: str = "n", epochs: int = 100):
        """
        训练检测模型
        
        Args:
            model_size: 模型大小 (n, s, m, l, x)
            epochs: 训练轮数
        """
        print(f"开始训练YOLOv8{model_size}人体检测模型...")
        
        # 准备数据集配置
        dataset_config_path = self.prepare_dataset_config()
        
        # 创建输出目录
        output_dir = Path("models/detection")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载预训练模型
        model = YOLO(f"yolov8{model_size}.pt")
        
        # 训练参数
        train_args = {
            "data": dataset_config_path,
            "epochs": epochs,
            "imgsz": self.detection_config.get("input_size", [640, 640])[0],
            "batch": self.training_config.get("batch_size", 16),
            "device": self.device,
            "project": str(output_dir),
            "name": f"yolov8{model_size}_human_{int(time.time())}",
            "save": True,
            "save_period": 10,
            "val": True,
            "plots": True,
            "verbose": True,
            "patience": self.training_config.get("early_stopping", {}).get("patience", 20),
            "lr0": self.training_config.get("learning_rate", 0.01),
            "weight_decay": self.training_config.get("weight_decay", 0.0005),
            "warmup_epochs": self.training_config.get("scheduler", {}).get("warmup_epochs", 3),
            "cos_lr": True,  # 使用余弦学习率调度
            "close_mosaic": 10,  # 最后10个epoch关闭mosaic增强
        }
        
        # 数据增强参数
        augmentation = self.training_config.get("augmentation", {})
        if augmentation.get("enabled", True):
            train_args.update({
                "hsv_h": augmentation.get("hue", 0.015),
                "hsv_s": augmentation.get("saturation", 0.7),
                "hsv_v": augmentation.get("brightness", 0.4),
                "degrees": augmentation.get("rotation", 0.0),
                "translate": 0.1,
                "scale": 0.5,
                "shear": 0.0,
                "perspective": 0.0,
                "flipud": 0.0,
                "fliplr": augmentation.get("horizontal_flip", 0.5),
                "mosaic": 1.0,
                "mixup": 0.0,
            })
        
        print("训练参数:")
        for key, value in train_args.items():
            print(f"  {key}: {value}")
            
        # 开始训练
        start_time = time.time()
        
        try:
            results = model.train(**train_args)
            
            training_time = time.time() - start_time
            print(f"\n训练完成！总耗时: {training_time/3600:.2f}小时")
            
            # 保存训练信息
            training_info = {
                "model_size": model_size,
                "epochs": epochs,
                "training_time_hours": training_time / 3600,
                "device": self.device,
                "train_args": train_args,
                "final_metrics": {
                    "mAP50": float(results.results_dict.get("metrics/mAP50(B)", 0)),
                    "mAP50_95": float(results.results_dict.get("metrics/mAP50-95(B)", 0)),
                    "precision": float(results.results_dict.get("metrics/precision(B)", 0)),
                    "recall": float(results.results_dict.get("metrics/recall(B)", 0)),
                }
            }
            
            # 保存训练信息
            info_path = output_dir / f"training_info_{model_size}_{int(time.time())}.json"
            with open(info_path, "w", encoding="utf-8") as f:
                json.dump(training_info, f, ensure_ascii=False, indent=2)
                
            print(f"训练信息已保存: {info_path}")
            
            # 打印最终指标
            print("\n最终训练指标:")
            for metric, value in training_info["final_metrics"].items():
                print(f"  {metric}: {value:.4f}")
                
            return results, training_info
            
        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            return None, None
            
    def evaluate_model(self, model_path: str, dataset_config_path: str = None):
        """
        评估训练好的模型
        
        Args:
            model_path: 模型路径
            dataset_config_path: 数据集配置路径
        """
        print(f"评估模型: {model_path}")
        
        if dataset_config_path is None:
            dataset_config_path = self.prepare_dataset_config()
            
        # 加载模型
        model = YOLO(model_path)
        
        # 运行验证
        results = model.val(
            data=dataset_config_path,
            imgsz=self.detection_config.get("input_size", [640, 640])[0],
            batch=self.training_config.get("batch_size", 16),
            device=self.device,
            save_json=True,
            save_hybrid=True,
            conf=self.detection_config.get("confidence_threshold", 0.5),
            iou=self.detection_config.get("iou_threshold", 0.45),
            project="results/detection",
            name=f"evaluation_{int(time.time())}"
        )
        
        # 提取评估指标
        metrics = {
            "mAP50": float(results.box.map50),
            "mAP50_95": float(results.box.map),
            "precision": float(results.box.mp),
            "recall": float(results.box.mr),
            "f1_score": 2 * (results.box.mp * results.box.mr) / (results.box.mp + results.box.mr) if (results.box.mp + results.box.mr) > 0 else 0
        }
        
        print("评估结果:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")
            
        # 检查是否满足目标指标
        target_accuracy = self.detection_config.get("target_accuracy", 0.8)
        if metrics["mAP50"] >= target_accuracy:
            print(f"✅ 模型满足目标准确率要求 (mAP50 >= {target_accuracy})")
        else:
            print(f"❌ 模型未满足目标准确率要求 (mAP50 < {target_accuracy})")
            
        return metrics
        
    def test_inference_speed(self, model_path: str, num_tests: int = 100):
        """
        测试推理速度
        
        Args:
            model_path: 模型路径
            num_tests: 测试次数
        """
        print(f"测试推理速度: {model_path}")
        
        # 创建检测器
        detector = HumanDetector(model_path=model_path, device=self.device)
        
        # 创建测试图像
        test_image = torch.randint(0, 255, (640, 640, 3), dtype=torch.uint8).numpy()
        
        # 预热
        for _ in range(10):
            detector.detect(test_image)
            
        # 测试推理速度
        times = []
        for _ in range(num_tests):
            start_time = time.time()
            detector.detect(test_image)
            times.append((time.time() - start_time) * 1000)  # 转换为毫秒
            
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"推理速度测试结果 ({num_tests}次测试):")
        print(f"  平均时间: {avg_time:.2f}ms")
        print(f"  最小时间: {min_time:.2f}ms")
        print(f"  最大时间: {max_time:.2f}ms")
        print(f"  平均FPS: {1000/avg_time:.1f}")
        
        # 检查是否满足实时性要求
        target_time = self.detection_config.get("target_time_ms", 200)
        if avg_time <= target_time:
            print(f"✅ 满足实时性要求 (<{target_time}ms)")
        else:
            print(f"❌ 未满足实时性要求 (>{target_time}ms)")
            
        return {
            "avg_time_ms": avg_time,
            "min_time_ms": min_time,
            "max_time_ms": max_time,
            "fps": 1000 / avg_time,
            "meets_realtime_requirement": avg_time <= target_time
        }

def main():
    """主函数"""
    print("人体检测模型训练程序")
    print("=" * 50)
    
    # 创建训练器
    trainer = DetectionTrainer()
    
    # 训练参数
    model_size = "n"  # 使用nano模型以确保速度
    epochs = 50  # 减少训练轮数以节省时间
    
    print(f"训练配置:")
    print(f"  模型大小: YOLOv8{model_size}")
    print(f"  训练轮数: {epochs}")
    print(f"  设备: {trainer.device}")
    
    # 开始训练
    results, training_info = trainer.train_model(model_size=model_size, epochs=epochs)
    
    if results is not None:
        # 获取最佳模型路径
        best_model_path = results.save_dir / "weights" / "best.pt"
        
        if best_model_path.exists():
            print(f"\n最佳模型路径: {best_model_path}")
            
            # 评估模型
            print("\n" + "=" * 50)
            print("模型评估")
            print("=" * 50)
            metrics = trainer.evaluate_model(str(best_model_path))
            
            # 测试推理速度
            print("\n" + "=" * 50)
            print("推理速度测试")
            print("=" * 50)
            speed_results = trainer.test_inference_speed(str(best_model_path))
            
            # 保存完整结果
            final_results = {
                "training_info": training_info,
                "evaluation_metrics": metrics,
                "speed_test": speed_results,
                "model_path": str(best_model_path)
            }
            
            results_path = Path("results/detection") / f"final_results_{int(time.time())}.json"
            results_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(results_path, "w", encoding="utf-8") as f:
                json.dump(final_results, f, ensure_ascii=False, indent=2)
                
            print(f"\n完整结果已保存: {results_path}")
            
        else:
            print("未找到训练好的模型文件")
    else:
        print("训练失败")

if __name__ == "__main__":
    main()
