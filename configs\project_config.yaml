# 浓烟环境人体目标判别项目配置文件

# 项目基本信息
project:
  name: "浓烟环境人体目标判别"
  version: "1.0.0"
  description: "开发智能算法系统，用于在浓烟环境中准确识别和定位被困人员"

# 数据集配置
dataset:
  name: "wuxi_video_2"
  path: "wuxi_video_2"
  classes: ["person"]
  num_classes: 1
  image_size: [1920, 1080]
  train_split: 0.7
  val_split: 0.3
  
  # 数据统计
  stats:
    total_images: 3187
    train_images: 2245
    val_images: 942
    total_objects: 3190
    avg_objects_per_image: 1.0

# 模型配置
models:
  # 去烟算法配置
  dehazing:
    type: "AOD-Net"  # 可选: DCP, AOD-Net, DehazeNet
    input_size: [640, 640]
    target_time_ms: 100  # 目标处理时间
    
  # 人体检测配置
  detection:
    type: "YOLOv8"  # 可选: YOLOv8, YOLOv5, SSD, Faster-RCNN
    model_size: "n"  # 可选: n, s, m, l, x
    input_size: [640, 640]
    confidence_threshold: 0.5
    iou_threshold: 0.45
    target_time_ms: 200  # 目标处理时间
    target_accuracy: 0.8  # 目标准确率
    
  # 双模态融合配置
  fusion:
    type: "attention"  # 可选: feature, decision, attention
    target_accuracy: 0.85  # 目标准确率
    target_recall: 0.999  # 目标召回率

# 训练配置
training:
  # 通用训练参数
  batch_size: 16
  epochs: 100
  learning_rate: 0.001
  weight_decay: 0.0005
  
  # 数据增强
  augmentation:
    enabled: true
    horizontal_flip: 0.5
    vertical_flip: 0.0
    rotation: 10
    brightness: 0.2
    contrast: 0.2
    saturation: 0.2
    hue: 0.1
    
  # 优化器配置
  optimizer:
    type: "AdamW"  # 可选: Adam, AdamW, SGD
    momentum: 0.9  # 仅SGD使用
    
  # 学习率调度
  scheduler:
    type: "cosine"  # 可选: cosine, step, exponential
    warmup_epochs: 5
    min_lr: 0.00001

# 硬件配置
hardware:
  # 开发环境
  development:
    device: "auto"  # auto, cpu, cuda
    num_workers: 4
    pin_memory: true
    
  # 目标部署平台
  deployment:
    platform: "RK3588s"
    framework: "RKNN-toolkit2"
    precision: "int8"  # fp32, fp16, int8

# 评估指标
metrics:
  # 去烟算法评估
  dehazing:
    - "PSNR"
    - "SSIM"
    - "entropy"
    - "avg_gradient"
    
  # 检测算法评估
  detection:
    - "mAP50"
    - "mAP50-95"
    - "precision"
    - "recall"
    - "F1-score"
    
  # 性能评估
  performance:
    - "inference_time"
    - "fps"
    - "memory_usage"

# 输出配置
output:
  # 模型保存
  models_dir: "models"
  checkpoints_dir: "checkpoints"
  
  # 结果保存
  results_dir: "results"
  logs_dir: "logs"
  
  # 可视化
  visualization:
    save_predictions: true
    save_metrics_plots: true
    save_sample_images: true

# 实验配置
experiments:
  # 实验命名规则
  naming_pattern: "{model_type}_{dataset}_{timestamp}"
  
  # 自动保存配置
  auto_save:
    enabled: true
    save_interval: 10  # epochs
    
  # 早停配置
  early_stopping:
    enabled: true
    patience: 20
    monitor: "val_mAP50"
    mode: "max"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true
  
# 可视化配置
visualization:
  # 训练过程可视化
  training:
    plot_loss: true
    plot_metrics: true
    plot_lr: true
    
  # 结果可视化
  results:
    plot_predictions: true
    plot_confusion_matrix: true
    plot_pr_curve: true
