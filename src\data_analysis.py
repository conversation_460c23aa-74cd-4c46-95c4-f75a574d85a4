#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集分析工具
用于分析浓烟环境人体目标判别项目的数据集
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from collections import defaultdict
import json

class DatasetAnalyzer:
    def __init__(self, dataset_path):
        """
        初始化数据集分析器
        
        Args:
            dataset_path (str): 数据集根目录路径
        """
        self.dataset_path = Path(dataset_path)
        self.image_path = self.dataset_path / "image"
        self.images_path = self.dataset_path / "images"
        self.labels_path = self.dataset_path / "labels"
        self.train_labels_path = self.labels_path / "train"
        self.val_labels_path = self.labels_path / "val"
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.analysis_results = {}
        
    def analyze_dataset_structure(self):
        """分析数据集结构"""
        print("=" * 50)
        print("数据集结构分析")
        print("=" * 50)
        
        structure = {
            "dataset_path": str(self.dataset_path),
            "image_count": 0,
            "images_count": 0,
            "train_labels_count": 0,
            "val_labels_count": 0
        }
        
        # 统计图像数量
        if self.image_path.exists():
            image_files = list(self.image_path.glob("*.jpg"))
            structure["image_count"] = len(image_files)
            print(f"image目录图像数量: {structure['image_count']}")
            
        if self.images_path.exists():
            images_files = list(self.images_path.glob("*.jpg"))
            structure["images_count"] = len(images_files)
            print(f"images目录图像数量: {structure['images_count']}")
            
        # 统计标签数量
        if self.train_labels_path.exists():
            train_labels = list(self.train_labels_path.glob("*.txt"))
            structure["train_labels_count"] = len(train_labels)
            print(f"训练集标签数量: {structure['train_labels_count']}")
            
        if self.val_labels_path.exists():
            val_labels = list(self.val_labels_path.glob("*.txt"))
            structure["val_labels_count"] = len(val_labels)
            print(f"验证集标签数量: {structure['val_labels_count']}")
            
        # 读取类别信息
        classes_file = self.labels_path / "classes.txt.txt"
        if classes_file.exists():
            with open(classes_file, 'r', encoding='utf-8') as f:
                classes = [line.strip() for line in f.readlines() if line.strip()]
            structure["classes"] = classes
            print(f"类别数量: {len(classes)}")
            print(f"类别: {classes}")
        
        self.analysis_results["structure"] = structure
        return structure
        
    def analyze_image_properties(self, sample_size=100):
        """分析图像属性"""
        print("\n" + "=" * 50)
        print("图像属性分析")
        print("=" * 50)
        
        # 选择要分析的图像目录
        if self.image_path.exists():
            image_dir = self.image_path
        elif self.images_path.exists():
            image_dir = self.images_path
        else:
            print("未找到图像目录")
            return
            
        image_files = list(image_dir.glob("*.jpg"))
        
        if len(image_files) == 0:
            print("未找到图像文件")
            return
            
        # 随机采样
        sample_files = np.random.choice(image_files, 
                                      min(sample_size, len(image_files)), 
                                      replace=False)
        
        widths, heights, channels = [], [], []
        file_sizes = []
        
        print(f"分析 {len(sample_files)} 张图像...")
        
        for img_file in sample_files:
            try:
                # 读取图像
                img = cv2.imread(str(img_file))
                if img is not None:
                    h, w, c = img.shape
                    heights.append(h)
                    widths.append(w)
                    channels.append(c)
                    
                    # 文件大小
                    file_size = img_file.stat().st_size / 1024  # KB
                    file_sizes.append(file_size)
                    
            except Exception as e:
                print(f"读取图像失败 {img_file}: {e}")
                
        if len(widths) > 0:
            image_props = {
                "width_mean": np.mean(widths),
                "width_std": np.std(widths),
                "width_min": np.min(widths),
                "width_max": np.max(widths),
                "height_mean": np.mean(heights),
                "height_std": np.std(heights),
                "height_min": np.min(heights),
                "height_max": np.max(heights),
                "channels": channels[0] if channels else 0,
                "file_size_mean_kb": np.mean(file_sizes),
                "file_size_std_kb": np.std(file_sizes),
                "sample_count": len(widths)
            }
            
            print(f"图像尺寸统计:")
            print(f"  宽度: {image_props['width_mean']:.1f}±{image_props['width_std']:.1f} "
                  f"({image_props['width_min']}-{image_props['width_max']})")
            print(f"  高度: {image_props['height_mean']:.1f}±{image_props['height_std']:.1f} "
                  f"({image_props['height_min']}-{image_props['height_max']})")
            print(f"  通道数: {image_props['channels']}")
            print(f"  文件大小: {image_props['file_size_mean_kb']:.1f}±{image_props['file_size_std_kb']:.1f} KB")
            
            self.analysis_results["image_properties"] = image_props
            return image_props
        else:
            print("无法读取任何图像")
            return None
            
    def analyze_annotations(self):
        """分析标注信息"""
        print("\n" + "=" * 50)
        print("标注信息分析")
        print("=" * 50)
        
        train_annotations = self._parse_annotations(self.train_labels_path, "训练集")
        val_annotations = self._parse_annotations(self.val_labels_path, "验证集")
        
        # 合并统计
        all_annotations = {
            "train": train_annotations,
            "val": val_annotations
        }
        
        self.analysis_results["annotations"] = all_annotations
        return all_annotations
        
    def _parse_annotations(self, labels_path, dataset_name):
        """解析标注文件"""
        if not labels_path.exists():
            print(f"{dataset_name}标注目录不存在")
            return None
            
        label_files = list(labels_path.glob("*.txt"))
        
        bbox_data = []
        files_with_objects = 0
        total_objects = 0
        
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                    
                objects_in_file = 0
                for line in lines:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            
                            bbox_data.append({
                                'class_id': class_id,
                                'x_center': x_center,
                                'y_center': y_center,
                                'width': width,
                                'height': height,
                                'area': width * height
                            })
                            objects_in_file += 1
                            total_objects += 1
                            
                if objects_in_file > 0:
                    files_with_objects += 1
                    
            except Exception as e:
                print(f"解析标注文件失败 {label_file}: {e}")
                
        if bbox_data:
            df = pd.DataFrame(bbox_data)
            
            stats = {
                "total_files": len(label_files),
                "files_with_objects": files_with_objects,
                "total_objects": total_objects,
                "avg_objects_per_file": total_objects / files_with_objects if files_with_objects > 0 else 0,
                "bbox_width_mean": df['width'].mean(),
                "bbox_width_std": df['width'].std(),
                "bbox_height_mean": df['height'].mean(),
                "bbox_height_std": df['height'].std(),
                "bbox_area_mean": df['area'].mean(),
                "bbox_area_std": df['area'].std(),
                "class_distribution": df['class_id'].value_counts().to_dict()
            }
            
            print(f"\n{dataset_name}统计:")
            print(f"  总文件数: {stats['total_files']}")
            print(f"  有目标的文件数: {stats['files_with_objects']}")
            print(f"  总目标数: {stats['total_objects']}")
            print(f"  平均每文件目标数: {stats['avg_objects_per_file']:.2f}")
            print(f"  边界框宽度: {stats['bbox_width_mean']:.3f}±{stats['bbox_width_std']:.3f}")
            print(f"  边界框高度: {stats['bbox_height_mean']:.3f}±{stats['bbox_height_std']:.3f}")
            print(f"  边界框面积: {stats['bbox_area_mean']:.3f}±{stats['bbox_area_std']:.3f}")
            print(f"  类别分布: {stats['class_distribution']}")
            
            return stats
        else:
            print(f"{dataset_name}无有效标注数据")
            return None
            
    def visualize_sample_images(self, num_samples=6):
        """可视化样本图像"""
        print("\n" + "=" * 50)
        print("样本图像可视化")
        print("=" * 50)

        try:
            # 设置matplotlib后端为Agg（无GUI）
            import matplotlib
            matplotlib.use('Agg')

            # 选择图像目录
            if self.image_path.exists():
                image_dir = self.image_path
            elif self.images_path.exists():
                image_dir = self.images_path
            else:
                print("未找到图像目录")
                return

            image_files = list(image_dir.glob("*.jpg"))

            if len(image_files) == 0:
                print("未找到图像文件")
                return

            # 随机选择样本
            sample_files = np.random.choice(image_files,
                                          min(num_samples, len(image_files)),
                                          replace=False)

            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            axes = axes.flatten()

            for i, img_file in enumerate(sample_files):
                if i >= len(axes):
                    break

                try:
                    img = cv2.imread(str(img_file))
                    if img is not None:
                        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        axes[i].imshow(img_rgb)
                        axes[i].set_title(f"{img_file.name}\n{img.shape[1]}x{img.shape[0]}")
                        axes[i].axis('off')

                except Exception as e:
                    print(f"显示图像失败 {img_file}: {e}")

            plt.tight_layout()
            plt.savefig('sample_images.png', dpi=150, bbox_inches='tight')
            print("样本图像已保存到 sample_images.png")
            plt.close()

        except Exception as e:
            print(f"可视化过程出错: {e}")
            print("跳过可视化步骤")
        
    def save_analysis_report(self, output_file="dataset_analysis_report.json"):
        """保存分析报告"""
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        converted_results = convert_numpy_types(self.analysis_results)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, ensure_ascii=False, indent=2)
        print(f"\n分析报告已保存到: {output_file}")
        
    def run_full_analysis(self):
        """运行完整的数据集分析"""
        print("开始数据集分析...")
        
        # 分析数据集结构
        self.analyze_dataset_structure()
        
        # 分析图像属性
        self.analyze_image_properties()
        
        # 分析标注信息
        self.analyze_annotations()
        
        # 可视化样本图像
        self.visualize_sample_images()
        
        # 保存分析报告
        self.save_analysis_report()
        
        print("\n数据集分析完成！")
        return self.analysis_results

if __name__ == "__main__":
    # 数据集路径
    dataset_path = "wuxi_video_2"
    
    # 创建分析器并运行分析
    analyzer = DatasetAnalyzer(dataset_path)
    results = analyzer.run_full_analysis()
